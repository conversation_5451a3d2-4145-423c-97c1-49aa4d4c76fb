{"version": 3, "file": "ExponentPrint.js", "sourceRoot": "", "sources": ["../src/ExponentPrint.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,MAAM,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;AAErD,eAAe,WAAW,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nconst printModule = requireNativeModule('ExpoPrint');\n\nexport default printModule;\n"]}