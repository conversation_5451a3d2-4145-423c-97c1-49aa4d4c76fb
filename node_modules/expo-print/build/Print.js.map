{"version": 3, "file": "Print.js", "sourceRoot": "", "sources": ["../src/Print.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AACxD,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,aAAa,MAAM,iBAAiB,CAAC;AAW5C,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,2BAA2B;AAC3B;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAoB,aAAa,CAAC,WAAW,CAAC;AAEtE,cAAc;AACd;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,KAAK,UAAU,UAAU,CAAC,OAAqB;IACpD,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;QACzB,OAAO,MAAM,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC3C;IACD,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;QACzF,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;KACjE;IACD,IAAI,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;KACzF;IACD,IAAI,OAAO,CAAC,kBAAkB,KAAK,SAAS,EAAE;QAC5C,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;KAC9F;IACD,IAAI,UAAU,EAAE;QACd,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;KACjE;IAED,UAAU,GAAG,IAAI,CAAC;IAClB,IAAI;QACF,OAAO,MAAM,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC3C;YAAS;QACR,UAAU,GAAG,KAAK,CAAC;KACpB;AACH,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB;IACtC,IAAI,aAAa,CAAC,aAAa,EAAE;QAC/B,OAAO,MAAM,aAAa,CAAC,aAAa,EAAE,CAAC;KAC5C;IAED,MAAM,IAAI,mBAAmB,CAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;AAC/D,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CAAC,UAA4B,EAAE;IACnE,OAAO,MAAM,aAAa,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AACvD,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\nimport { Platform } from 'react-native';\n\nimport ExponentPrint from './ExponentPrint';\nimport {\n  FilePrintOptions,\n  FilePrintResult,\n  OrientationType,\n  PrintOptions,\n  Printer,\n} from './Print.types';\n\nexport { FilePrintOptions, FilePrintResult, OrientationType, PrintOptions, Printer };\n\nlet isPrinting = false;\n// @needsAudit @docsMissing\n/**\n * The orientation of the printed content.\n */\nexport const Orientation: OrientationType = ExponentPrint.Orientation;\n\n// @needsAudit\n/**\n * Prints a document or HTML, on web this prints the HTML from the page.\n * > Note: On iOS, printing from HTML source doesn't support local asset URLs (due to `WKWebView`\n * > limitations). As a workaround you can use inlined base64-encoded strings.\n * > See [this comment](https://github.com/expo/expo/issues/7940#issuecomment-*********) for more details.\n *\n * > Note: on iOS, when printing without providing a `PrintOptions.printerUrl` the `Promise` will be\n * > resolved once printing is started in the native print window and rejected if the window is closed without\n * > starting the print. On Android the `Promise` will be resolved immediately after displaying the native print window\n * > and won't be rejected if the window is closed without starting the print.\n * @param options A map defining what should be printed.\n * @return Resolves to an empty `Promise` if printing started.\n */\nexport async function printAsync(options: PrintOptions): Promise<void> {\n  if (Platform.OS === 'web') {\n    return await ExponentPrint.print(options);\n  }\n  if (!options.uri && !options.html && Platform.OS === 'ios' && !options.markupFormatterIOS) {\n    throw new Error('Must provide either `html` or `uri` to print');\n  }\n  if (options.uri && options.html) {\n    throw new Error('Must provide exactly one of `html` and `uri` but both were specified');\n  }\n  if (options.markupFormatterIOS !== undefined) {\n    console.warn('The markupFormatterIOS option is deprecated. Use useMarkupFormatter instead.');\n  }\n  if (isPrinting) {\n    throw new Error('Another print request is already in progress');\n  }\n\n  isPrinting = true;\n  try {\n    return await ExponentPrint.print(options);\n  } finally {\n    isPrinting = false;\n  }\n}\n\n// @needsAudit\n/**\n * Chooses a printer that can be later used in `printAsync`\n * @return A promise which fulfils with an object containing `name` and `url` of the selected printer.\n * @platform ios\n */\nexport async function selectPrinterAsync(): Promise<Printer> {\n  if (ExponentPrint.selectPrinter) {\n    return await ExponentPrint.selectPrinter();\n  }\n\n  throw new UnavailabilityError('Print', 'selectPrinterAsync');\n}\n\n// @needsAudit\n/**\n * Prints HTML to PDF file and saves it to [app's cache directory](./filesystem/#filesystemcachedirectory).\n * On Web this method opens the print dialog.\n * @param options A map of print options.\n */\nexport async function printToFileAsync(options: FilePrintOptions = {}): Promise<FilePrintResult> {\n  return await ExponentPrint.printToFileAsync(options);\n}\n"]}