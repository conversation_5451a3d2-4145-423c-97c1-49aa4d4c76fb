{"version": 3, "file": "Print.types.js", "sourceRoot": "", "sources": ["../src/Print.types.ts"], "names": [], "mappings": "", "sourcesContent": ["// @needsAudit\nexport type PrintOptions = {\n  /**\n   * URI of a PDF file to print. Remote, local (ex. selected via `DocumentPicker`) or base64 data URI\n   * starting with `data:application/pdf;base64,`. This only supports PDF, not other types of\n   * document (e.g. images).\n   */\n  uri?: string;\n  /**\n   * HTML string to print.\n   * @platform android\n   * @platform ios\n   */\n  html?: string;\n  /**\n   * Width of the single page in pixels. Defaults to `612` which is a width of US Letter paper\n   * format with 72 PPI. **Available only with `html` option.**\n   */\n  width?: number;\n  /**\n   * Height of the single page in pixels. Defaults to `792` which is a height of US Letter paper\n   * format with 72 PPI. **Available only with `html` option.**\n   */\n  height?: number;\n  /**\n   * URL of the printer to use. Returned from `selectPrinterAsync`.\n   * @platform ios\n   */\n  printerUrl?: string;\n  /**\n   * Alternative to default option that uses [UIMarkupTextPrintFormatter](https://developer.apple.com/documentation/uikit/uimarkuptextprintformatter)\n   * instead of WebView, but it doesn't display images.\n   * @platform ios\n   */\n  useMarkupFormatter?: boolean;\n  /**\n   * @deprecated\n   * This argument is deprecated, use `useMarkupFormatter` instead.\n   * Might be removed in the future releases.\n   * @platform ios\n   */\n  markupFormatterIOS?: string;\n  /**\n   * The orientation of the printed content, `Print.Orientation.portrait`\n   * or `Print.Orientation.landscape`.\n   * @platform ios\n   */\n  orientation?: OrientationType['portrait'] | OrientationType['landscape'];\n  /**\n   * Page margins for the printed document.\n   * @platform ios\n   */\n  margins?: PageMargins;\n};\n\n// @needsAudit\nexport type Printer = {\n  /**\n   * Name of the printer.\n   */\n  name: string;\n  /**\n   * URL of the printer.\n   */\n  url: string;\n};\n\n// @needsAudit\n/**\n * The possible values of orientation for the printed content.\n */\nexport interface OrientationType {\n  portrait: string;\n  landscape: string;\n}\n\n// @needsAudit\nexport type PageMargins = {\n  top: number;\n  right: number;\n  bottom: number;\n  left: number;\n};\n\n// @docsMissing\nexport type FilePrintOptions = {\n  /**\n   * HTML string to print into PDF file.\n   */\n  html?: string;\n  /**\n   * Alternative to default option that uses [UIMarkupTextPrintFormatter](https://developer.apple.com/documentation/uikit/uimarkuptextprintformatter)\n   * instead of WebView, but it doesn't display images.\n   * @platform ios\n   */\n  useMarkupFormatter?: boolean;\n  /**\n   * Width of the single page in pixels. Defaults to `612` which is a width of US Letter paper\n   * format with 72 PPI.\n   */\n  width?: number;\n  /**\n   * Height of the single page in pixels. Defaults to `792` which is a height of US Letter paper\n   * format with 72 PPI.\n   */\n  height?: number;\n  /**\n   * Page margins for the printed document.\n   * @platform ios\n   */\n  margins?: PageMargins;\n  /**\n   * Whether to include base64 encoded string of the file in the returned object.\n   */\n  base64?: boolean;\n};\n\n// @needsAudit\nexport type FilePrintResult = {\n  /**\n   * A URI to the printed PDF file.\n   */\n  uri: string;\n  /**\n   * Number of pages that were needed to render given content.\n   */\n  numberOfPages: number;\n  /**\n   * Base64 encoded string containing the data of the PDF file. **Available only if `base64`\n   * option is truthy**. It doesn't include data URI prefix `data:application/pdf;base64,`.\n   */\n  base64?: string;\n};\n"]}