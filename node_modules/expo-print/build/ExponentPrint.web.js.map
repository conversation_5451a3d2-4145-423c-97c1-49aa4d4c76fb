{"version": 3, "file": "ExponentPrint.web.js", "sourceRoot": "", "sources": ["../src/ExponentPrint.web.ts"], "names": [], "mappings": "AAEA,eAAe;IACb,IAAI,IAAI;QACN,OAAO,eAAe,CAAC;IACzB,CAAC;IACD,IAAI,WAAW;QACb,OAAO;YACL,QAAQ,EAAE,UAAU;YACpB,SAAS,EAAE,WAAW;SACvB,CAAC;IACJ,CAAC;IACD,KAAK,CAAC,KAAK;QACT,MAAM,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IACD,KAAK,CAAC,gBAAgB;QACpB,MAAM,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;CACF,CAAC", "sourcesContent": ["import { OrientationType } from './Print.types';\n\nexport default {\n  get name(): string {\n    return 'ExponentPrint';\n  },\n  get Orientation(): OrientationType {\n    return {\n      portrait: 'portrait',\n      landscape: 'landscape',\n    };\n  },\n  async print(): Promise<void> {\n    window.print();\n  },\n  async printToFileAsync(): Promise<void> {\n    window.print();\n  },\n};\n"]}