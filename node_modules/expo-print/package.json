{"name": "expo-print", "version": "12.8.1", "description": "Provides an API for iOS (AirPrint) and Android printing functionality.", "main": "build/Print.js", "types": "build/Print.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "print"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-print"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/print/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "43f1b4f8a5a9bca649e4e7ca6e4155482a162431"}