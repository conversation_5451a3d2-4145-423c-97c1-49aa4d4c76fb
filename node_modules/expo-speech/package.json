{"name": "expo-speech", "version": "11.7.0", "description": "Provides text-to-speech functionality.", "main": "build/Speech.js", "types": "build/Speech.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-speech", "speech", "voice"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-speech"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/speech/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "3142a086578deffd8704a8f1b6f0f661527d836c"}