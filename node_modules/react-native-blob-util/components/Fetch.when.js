// placeholder
{/*<Fetch method="GET"
  url={`www.example.com/avatar/${this.state.userId}`}
  onSuccess={(data) => { this.setState({avatar : data.path()}) }}
  onError={this.showError}>
  <Fetch.before>
		<Image souce="images/placeholder.png"/>
	</Fetch.before>
  <Fetch.success>
		<Image souce={this.state.avatar}/>
	</Fetch.success>
  <Fetch.error>
		<Image souce="images/error.png"/>
	</Fetch.error>
</LoginAction.onPress>*/}
