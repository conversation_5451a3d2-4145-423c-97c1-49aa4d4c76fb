apply plugin: 'com.android.library'
apply plugin: 'maven-publish'

def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

repositories {
    mavenCentral()
    mavenLocal()
    maven {
        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
        url "$rootDir/../node_modules/react-native/android"
    }
    maven {
        // Android JSC is installed from npm
        url "$rootDir/../node_modules/jsc-android/dist"
    }
    google()
}

buildscript {
     // The Android Gradle plugin is only required when opening the android folder stand-alone.
    // This avoids unnecessary downloads and potential conflicts when the library is included as a
    // module dependency in an application project.
    // ref: https://docs.gradle.org/current/userguide/tutorial_using_tasks.html#sec:build_script_external_dependencies
    if (project == rootProject) {
        repositories {
            google()
            mavenCentral()
        }
        dependencies {
            classpath 'com.android.tools.build:gradle:7.2.1'
        }
    }
}

def isNewArchitectureEnabled() {
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

if (isNewArchitectureEnabled()) {
    apply plugin: 'com.facebook.react'
}

android {
    def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
    if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
        namespace "com.ReactNativeBlobUtil"
        buildFeatures {
            buildConfig true
        }
    }
    compileSdkVersion safeExtGet('compileSdkVersion', 30)
    buildToolsVersion safeExtGet('buildToolsVersion', '28.0.3')
    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 16)
        targetSdkVersion safeExtGet('targetSdkVersion', 30)
        versionCode 1
        versionName "1.0"
        buildConfigField("boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString())
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
       sourceCompatibility JavaVersion.VERSION_1_8
       targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        abortOnError false
    }
    productFlavors {
    }
    sourceSets {
        main {
            if (isNewArchitectureEnabled()) {
                java.srcDirs += ['src/newarch']
            } else {
                java.srcDirs += ['src/oldarch']
            }
        }
    }
}

dependencies {
    implementation "com.facebook.react:react-native:${safeExtGet('reactNativeVersion', '+')}"
    implementation 'org.apache.commons:commons-lang3:3.0'
}

afterEvaluate { project ->
    // do this only when building this lib alone as a root project
    if (project != rootProject) {
        return
    }

    // some Gradle build hooks ref:
    // https://www.oreilly.com/library/view/gradle-beyond-the/9781449373801/ch03.html
    task androidJavadoc(type: Javadoc) {
        source = android.sourceSets.main.java.srcDirs
        classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
        classpath += files(android.libraryVariants.collect { variant ->
            variant.javaCompileProvider.get().classpath.files
        })
    }

    task androidJavadocJar(type: Jar, dependsOn: androidJavadoc) {
        classifier = 'javadoc'
        from androidJavadoc.destinationDir
    }

    task androidSourcesJar(type: Jar) {
        classifier = 'sources'
        from android.sourceSets.main.java.sourceFiles
    }

    android.libraryVariants.all { variant ->
        def name = variant.name.capitalize()
        def javaCompileTask = variant.javaCompileProvider.get()

        task "jar${name}"(type: Jar, dependsOn: javaCompileTask) {
            from javaCompileTask.destinationDir
        }
    }

    artifacts {
        archives androidSourcesJar
        archives androidJavadocJar
    }

    publishing {
        publications {
            mavenJava(MavenPublication) {
                def packageJson = new groovy.json.JsonSlurper().parseText(file('../package.json').text)

                artifactId packageJson.name
                groupId = "com.ReactNativeBlobUtil"
                version = packageJson.version

                pom {
                    name = packageJson.title
                    description = packageJson.description
                    url = packageJson.repository.baseUrl

                    licenses {
                        license {
                            name = packageJson.license
                            url = packageJson.repository.baseUrl + '/blob/master/' + packageJson.licenseFilename
                            distribution = 'repo'
                        }
                    }

                    developers {
                        developer {
                            id = packageJson.author.username
                            name = packageJson.author.name
                        }
                    }
                }
            }
        }
        repositories {
            maven {
                url = "file://${projectDir}/../android/maven"
            }
        }
    }
}