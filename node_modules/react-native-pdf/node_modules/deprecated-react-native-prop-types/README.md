# deprecated-react-native-prop-types

This package contains deprecated `prop-types` from [React Native](https://github.com/facebook/react-native).

## Image.propTypes

### Before

```js
import {Image} from 'react-native';

doSomething(Image.propTypes);
```

### After

```js
import {ImagePropTypes} from 'deprecated-react-native-prop-types';

doSomething(ImagePropTypes);
```

## Text.propTypes

### Before

```js
import {Text} from 'react-native';

doSomething(Text.propTypes);
```

### After

```js
import {TextPropTypes} from 'deprecated-react-native-prop-types';

doSomething(TextPropTypes);
```

## TextInput.propTypes

### Before

```js
import {TextInput} from 'react-native';

doSomething(TextInput.propTypes);
```

### After

```js
import {TextInputPropTypes} from 'deprecated-react-native-prop-types';

doSomething(TextInputPropTypes);
```

## ColorPropType

### Before

```js
import {ColorPropType} from 'react-native';

doSomething(ColorPropType);
```

### After

```js
import {ColorPropType} from 'deprecated-react-native-prop-types';

doSomething(ColorPropType);
```

## EdgeInsetsPropType

### Before

```js
import {EdgeInsetsPropType} from 'react-native';

doSomething(EdgeInsetsPropType);
```

### After

```js
import {EdgeInsetsPropType} from 'deprecated-react-native-prop-types';

doSomething(EdgeInsetsPropType);
```

## PointPropType

### Before

```js
import {PointPropType} from 'react-native';

doSomething(PointPropType);
```

### After

```js
import {PointPropType} from 'deprecated-react-native-prop-types';

doSomething(PointPropType);
```

## ViewPropTypes

### Before

```js
import {ViewPropTypes} from 'react-native';

doSomething(ViewPropTypes);
```

### After

```js
import {ViewPropTypes} from 'deprecated-react-native-prop-types';

doSomething(ViewPropTypes);
```
