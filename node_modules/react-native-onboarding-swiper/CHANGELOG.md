## 1.1.1 (April 15, 2020)

* fix RTL
* use `useNativeDrive` for RN v0.62+

## 1.1.0 (May 01, 2019)

* add more props for special cases

## 1.0.0 (January 04, 2019)

* stable release (no changes)

## 0.10.0 (November 25, 2018)

* override default pages styles

## 0.9.0 (October 29, 2018)

* add `pageIndexCallback` to get the page index on change

## 0.8.0 (October 21, 2018)

* add props for title's styles
* fix font scaling for symbol buttons

## 0.7.0 (July 27, 2018)

* add some more props and re-work how the statusbar color gets reset to default

## 0.6.0 (April 08, 2018)

* iPhone X support (thanks to @nbolender)
* new props: `flatlistProps` and `controlStatusBar`
* fade-in animation for `Done` button

## 0.5.0 (March 24, 2018)

* allow customization of image container styles
* fix several propTypes warnings

## 0.4.0 (February 23, 2018)

* Listen to orientation changes and adjust content appropiatly
* Add animations between page chagnes
* Make buttons, dots etc. adjustable via a wide range of different props
* Change next arrow to text button `next`.

## 0.3.0 (October 18, 2017)

* Increase touchable Radius for Buttons

## 0.2.0 (October 16, 2017)

* Change name to `react-native-onboarding-swiper`
* Change from ScrollView to FlatList
* Add `skipLabel` prop
* Title and subtitle can be components
* Change `onEnd` to `onSkip` and `onDone`
* Remove check mark dot because it's not useful
* Adapt StatusBar color
* Refactor most of the internal components

## 0.1.1 (October 11, 2016)

* Detect light background and adapt the text and controls to it.
* Allow to disable the bottom bar overlay via the `bottomOverlay` prop.
* Allow to disable either of the skip, next, or done buttons.

## 0.1.0 (October 10, 2016)

Initial release.
