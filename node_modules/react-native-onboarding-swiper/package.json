{"name": "react-native-onboarding-swiper", "version": "1.3.0", "description": "Delightful Onboarding for your React-Native App", "main": "src/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 0"}, "repository": {"type": "git", "url": "git+https://github.com/jfilter/react-native-onboarding-swiper"}, "keywords": ["React", "Native", "React-Native", "Component", "Onboarding", "Tutorial", "Intro", "Swiper", "Welcome", "Introduction", "Instructions"], "author": "<PERSON><PERSON>, <PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jfilter/react-native-onboarding-swiper/issues"}, "homepage": "https://github.com/jfilter/react-native-onboarding-swiper#readme", "dependencies": {"tinycolor2": "^1.4.1"}, "devDependencies": {"babel-eslint": "^10.1.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-react": "^7.5.1", "eslint-plugin-react-native": "^4.1.0", "prettier": "^1.9.2"}, "peerDependencies": {"prop-types": "*", "react": "*", "react-native": "*"}, "prettier": {"singleQuote": true, "trailingComma": "es5"}}