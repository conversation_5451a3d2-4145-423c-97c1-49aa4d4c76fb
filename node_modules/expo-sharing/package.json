{"name": "expo-sharing", "version": "11.10.0", "description": "ExpoSharing standalone module", "main": "build/Sharing.js", "types": "build/Sharing.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-sharing"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-sharing"}, "jest": {"preset": "expo-module-scripts"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/sharing/", "dependencies": {}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "6aca7ce098ddc667776a3d7cf612adbb985e264a"}