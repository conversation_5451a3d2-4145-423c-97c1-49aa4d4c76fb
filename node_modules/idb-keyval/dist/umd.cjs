function _slicedToArray(n,t){return _arrayWithHoles(n)||_iterableToArrayLimit(n,t)||_unsupportedIterableToArray(n,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(n,t){if(n){if("string"==typeof n)return _arrayLikeToArray(n,t);var r={}.toString.call(n).slice(8,-1);return"Object"===r&&n.constructor&&(r=n.constructor.name),"Map"===r||"Set"===r?Array.from(n):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(n,t):void 0}}function _arrayLikeToArray(n,t){(null==t||t>n.length)&&(t=n.length);for(var r=0,e=Array(t);r<t;r++)e[r]=n[r];return e}function _iterableToArrayLimit(n,t){var r=null==n?null:"undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(null!=r){var e,o,u,i,a=[],c=!0,l=!1;try{if(u=(r=r.call(n)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(e=u.call(r)).done)&&(a.push(e.value),a.length!==t);c=!0);}catch(n){l=!0,o=n}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(l)throw o}}return a}}function _arrayWithHoles(n){if(Array.isArray(n))return n}function _typeof(n){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(n){return typeof n}:function(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},_typeof(n)}!function(n,t){"object"===("undefined"==typeof exports?"undefined":_typeof(exports))&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n="undefined"!=typeof globalThis?globalThis:n||self).idbKeyval={})}(this,(function(n){"use strict";function t(n){return new Promise((function(t,r){n.oncomplete=n.onsuccess=function(){return t(n.result)},n.onabort=n.onerror=function(){return r(n.error)}}))}function r(n,r){var e;return function(o,u){return function(){if(e)return e;var o=indexedDB.open(n);return o.onupgradeneeded=function(){return o.result.createObjectStore(r)},(e=t(o)).then((function(n){n.onclose=function(){return e=void 0}}),(function(){})),e}().then((function(n){return u(n.transaction(r,o).objectStore(r))}))}}var e;function o(){return e||(e=r("keyval-store","keyval")),e}function u(n,r){return n.openCursor().onsuccess=function(){this.result&&(r(this.result),this.result.continue())},t(n.transaction)}n.clear=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:o())("readwrite",(function(n){return n.clear(),t(n.transaction)}))},n.createStore=r,n.del=function(n){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:o())("readwrite",(function(r){return r.delete(n),t(r.transaction)}))},n.delMany=function(n){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:o())("readwrite",(function(r){return n.forEach((function(n){return r.delete(n)})),t(r.transaction)}))},n.entries=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o();return n("readonly",(function(r){if(r.getAll&&r.getAllKeys)return Promise.all([t(r.getAllKeys()),t(r.getAll())]).then((function(n){var t=_slicedToArray(n,2),r=t[0],e=t[1];return r.map((function(n,t){return[n,e[t]]}))}));var e=[];return n("readonly",(function(n){return u(n,(function(n){return e.push([n.key,n.value])})).then((function(){return e}))}))}))},n.get=function(n){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:o())("readonly",(function(r){return t(r.get(n))}))},n.getMany=function(n){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:o())("readonly",(function(r){return Promise.all(n.map((function(n){return t(r.get(n))})))}))},n.keys=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:o())("readonly",(function(n){if(n.getAllKeys)return t(n.getAllKeys());var r=[];return u(n,(function(n){return r.push(n.key)})).then((function(){return r}))}))},n.promisifyRequest=t,n.set=function(n,r){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:o())("readwrite",(function(e){return e.put(r,n),t(e.transaction)}))},n.setMany=function(n){return(arguments.length>1&&void 0!==arguments[1]?arguments[1]:o())("readwrite",(function(r){return n.forEach((function(n){return r.put(n[1],n[0])})),t(r.transaction)}))},n.update=function(n,r){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:o())("readwrite",(function(e){return new Promise((function(o,u){e.get(n).onsuccess=function(){try{e.put(r(this.result),n),o(t(e.transaction))}catch(n){u(n)}}}))}))},n.values=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:o())("readonly",(function(n){if(n.getAll)return t(n.getAll());var r=[];return u(n,(function(n){return r.push(n.value)})).then((function(){return r}))}))}}));
