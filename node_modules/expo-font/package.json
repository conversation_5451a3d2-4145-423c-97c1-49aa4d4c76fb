{"name": "expo-font", "version": "11.10.3", "description": "Load fonts at runtime and use them in React Native components.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "font"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-font"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/font/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"fontfaceobserver": "^2.1.0"}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "6ceeb37fe2ccd9b290f9d717d9792c68487118b1"}