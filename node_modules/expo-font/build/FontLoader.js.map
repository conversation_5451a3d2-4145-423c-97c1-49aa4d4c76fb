{"version": 3, "file": "FontLoader.js", "sourceRoot": "", "sources": ["../src/FontLoader.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,SAAS,MAAM,gBAAgB,CAAC;AACvC,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAG9C,MAAM,UAAU,GAAG,SAAS,CAAC,YAAY,KAAK,MAAM,CAAC;AACrD,MAAM,iBAAiB,GAAG,SAAS,CAAC,YAAY,KAAK,YAAY,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC;AAE3F,MAAM,UAAU,sBAAsB,CAAC,IAAY;IACjD,OAAO,CACL,CAAC,UAAU,IAAI,iBAAiB,CAAC;QACjC,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;QACrC,IAAI,KAAK,QAAQ;QACjB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CACpC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,MAAkB;IAClD,IAAI,MAAM,YAAY,KAAK,EAAE;QAC3B,OAAO,MAAM,CAAC;KACf;IAED,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;KAC9B;SAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACrC,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;KACjC;SAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,WAAW,EAAE;QAC1E,OAAO,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACtC;IAED,oEAAoE;IACpE,uEAAuE;IACvE,yCAAyC;IACzC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,IAAY,EACZ,KAA2B;IAE3B,MAAM,KAAK,GAAG,KAAc,CAAC;IAC7B,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;QACxB,MAAM,IAAI,UAAU,CAClB,iBAAiB,EACjB,mFAAmF,CACpF,CAAC;KACH;IAED,MAAM,KAAK,CAAC,aAAa,EAAE,CAAC;IAC5B,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;QACrB,MAAM,IAAI,UAAU,CAAC,cAAc,EAAE,sCAAsC,IAAI,GAAG,CAAC,CAAC;KACrF;IACD,MAAM,cAAc,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,IAAY;IAC5C,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;QAChC,OAAO,GAAG,SAAS,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;KACzC;SAAM;QACL,OAAO,IAAI,CAAC;KACb;AACH,CAAC", "sourcesContent": ["import { Asset } from 'expo-asset';\nimport Constants from 'expo-constants';\nimport { CodedError } from 'expo-modules-core';\nimport { Platform } from 'react-native';\n\nimport ExpoFontLoader from './ExpoFontLoader';\nimport { FontResource, FontSource } from './Font.types';\n\nconst isInClient = Constants.appOwnership === 'expo';\nconst isInIOSStandalone = Constants.appOwnership === 'standalone' && Platform.OS === 'ios';\n\nexport function fontFamilyNeedsScoping(name: string): boolean {\n  return (\n    (isInClient || isInIOSStandalone) &&\n    !Constants.systemFonts.includes(name) &&\n    name !== 'System' &&\n    !name.includes(Constants.sessionId)\n  );\n}\n\nexport function getAssetForSource(source: FontSource): Asset | FontResource {\n  if (source instanceof Asset) {\n    return source;\n  }\n\n  if (typeof source === 'string') {\n    return Asset.fromURI(source);\n  } else if (typeof source === 'number') {\n    return Asset.fromModule(source);\n  } else if (typeof source === 'object' && typeof source.uri !== 'undefined') {\n    return getAssetForSource(source.uri);\n  }\n\n  // @ts-ignore Error: Type 'string' is not assignable to type 'Asset'\n  // We can't have a string here, we would have thrown an error if !isWeb\n  // or returned Asset.fromModule if isWeb.\n  return source;\n}\n\nexport async function loadSingleFontAsync(\n  name: string,\n  input: Asset | FontResource\n): Promise<void> {\n  const asset = input as Asset;\n  if (!asset.downloadAsync) {\n    throw new CodedError(\n      `ERR_FONT_SOURCE`,\n      '`loadSingleFontAsync` expected resource of type `Asset` from expo-asset on native'\n    );\n  }\n\n  await asset.downloadAsync();\n  if (!asset.downloaded) {\n    throw new CodedError(`ERR_DOWNLOAD`, `Failed to download asset for font \"${name}\"`);\n  }\n  await ExpoFontLoader.loadAsync(getNativeFontName(name), asset.localUri);\n}\n\nexport function getNativeFontName(name: string): string {\n  if (fontFamilyNeedsScoping(name)) {\n    return `${Constants.sessionId}-${name}`;\n  } else {\n    return name;\n  }\n}\n"]}