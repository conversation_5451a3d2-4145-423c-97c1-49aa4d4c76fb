import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetSearchSuggestionsRequest,
  GetSearchSuggestionsResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface GetSearchSuggestionsCommandInput
  extends GetSearchSuggestionsRequest {}
export interface GetSearchSuggestionsCommandOutput
  extends GetSearchSuggestionsResponse,
    __MetadataBearer {}
declare const GetSearchSuggestionsCommand_base: {
  new (
    input: GetSearchSuggestionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetSearchSuggestionsCommandInput,
    GetSearchSuggestionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetSearchSuggestionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetSearchSuggestionsCommandInput,
    GetSearchSuggestionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetSearchSuggestionsCommand extends GetSearchSuggestionsCommand_base {
  protected static __types: {
    api: {
      input: GetSearchSuggestionsRequest;
      output: GetSearchSuggestionsResponse;
    };
    sdk: {
      input: GetSearchSuggestionsCommandInput;
      output: GetSearchSuggestionsCommandOutput;
    };
  };
}
