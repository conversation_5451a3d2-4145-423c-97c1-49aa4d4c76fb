import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetDeviceFleetReportRequest,
  GetDeviceFleetReportResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface GetDeviceFleetReportCommandInput
  extends GetDeviceFleetReportRequest {}
export interface GetDeviceFleetReportCommandOutput
  extends GetDeviceFleetReportResponse,
    __MetadataBearer {}
declare const GetDeviceFleetReportCommand_base: {
  new (
    input: GetDeviceFleetReportCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDeviceFleetReportCommandInput,
    GetDeviceFleetReportCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetDeviceFleetReportCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetDeviceFleetReportCommandInput,
    GetDeviceFleetReportCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetDeviceFleetReportCommand extends GetDeviceFleetReportCommand_base {
  protected static __types: {
    api: {
      input: GetDeviceFleetReportRequest;
      output: GetDeviceFleetReportResponse;
    };
    sdk: {
      input: GetDeviceFleetReportCommandInput;
      output: GetDeviceFleetReportCommandOutput;
    };
  };
}
