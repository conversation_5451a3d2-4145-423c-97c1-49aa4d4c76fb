import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListStageDevicesRequest,
  ListStageDevicesResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListStageDevicesCommandInput extends ListStageDevicesRequest {}
export interface ListStageDevicesCommandOutput
  extends ListStageDevicesResponse,
    __MetadataBearer {}
declare const ListStageDevicesCommand_base: {
  new (
    input: ListStageDevicesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListStageDevicesCommandInput,
    ListStageDevicesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListStageDevicesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListStageDevicesCommandInput,
    ListStageDevicesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListStageDevicesCommand extends ListStageDevicesCommand_base {
  protected static __types: {
    api: {
      input: ListStageDevicesRequest;
      output: ListStageDevicesResponse;
    };
    sdk: {
      input: ListStageDevicesCommandInput;
      output: ListStageDevicesCommandOutput;
    };
  };
}
