import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListImageVersionsRequest,
  ListImageVersionsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListImageVersionsCommandInput
  extends ListImageVersionsRequest {}
export interface ListImageVersionsCommandOutput
  extends ListImageVersionsResponse,
    __MetadataBearer {}
declare const ListImageVersionsCommand_base: {
  new (
    input: ListImageVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListImageVersionsCommandInput,
    ListImageVersionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListImageVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListImageVersionsCommandInput,
    ListImageVersionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListImageVersionsCommand extends ListImageVersionsCommand_base {
  protected static __types: {
    api: {
      input: ListImageVersionsRequest;
      output: ListImageVersionsResponse;
    };
    sdk: {
      input: ListImageVersionsCommandInput;
      output: ListImageVersionsCommandOutput;
    };
  };
}
