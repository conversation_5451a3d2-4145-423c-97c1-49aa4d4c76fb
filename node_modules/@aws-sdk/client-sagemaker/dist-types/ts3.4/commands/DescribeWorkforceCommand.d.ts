import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeWorkforceRequest,
  DescribeWorkforceResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeWorkforceCommandInput
  extends DescribeWorkforceRequest {}
export interface DescribeWorkforceCommandOutput
  extends DescribeWorkforceResponse,
    __MetadataBearer {}
declare const DescribeWorkforceCommand_base: {
  new (
    input: DescribeWorkforceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeWorkforceCommandInput,
    DescribeWorkforceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeWorkforceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeWorkforceCommandInput,
    DescribeWorkforceCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeWorkforceCommand extends DescribeWorkforceCommand_base {
  protected static __types: {
    api: {
      input: DescribeWorkforceRequest;
      output: DescribeWorkforceResponse;
    };
    sdk: {
      input: DescribeWorkforceCommandInput;
      output: DescribeWorkforceCommandOutput;
    };
  };
}
