import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ImportHubContentRequest,
  ImportHubContentResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ImportHubContentCommandInput extends ImportHubContentRequest {}
export interface ImportHubContentCommandOutput
  extends ImportHubContentResponse,
    __MetadataBearer {}
declare const ImportHubContentCommand_base: {
  new (
    input: ImportHubContentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ImportHubContentCommandInput,
    ImportHubContentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ImportHubContentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ImportHubContentCommandInput,
    ImportHubContentCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ImportHubContentCommand extends ImportHubContentCommand_base {
  protected static __types: {
    api: {
      input: ImportHubContentRequest;
      output: ImportHubContentResponse;
    };
    sdk: {
      input: ImportHubContentCommandInput;
      output: ImportHubContentCommandOutput;
    };
  };
}
