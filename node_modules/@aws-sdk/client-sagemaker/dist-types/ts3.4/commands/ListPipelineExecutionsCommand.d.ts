import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListPipelineExecutionsRequest,
  ListPipelineExecutionsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListPipelineExecutionsCommandInput
  extends ListPipelineExecutionsRequest {}
export interface ListPipelineExecutionsCommandOutput
  extends ListPipelineExecutionsResponse,
    __MetadataBearer {}
declare const ListPipelineExecutionsCommand_base: {
  new (
    input: ListPipelineExecutionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListPipelineExecutionsCommandInput,
    ListPipelineExecutionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListPipelineExecutionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListPipelineExecutionsCommandInput,
    ListPipelineExecutionsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListPipelineExecutionsCommand extends ListPipelineExecutionsCommand_base {
  protected static __types: {
    api: {
      input: ListPipelineExecutionsRequest;
      output: ListPipelineExecutionsResponse;
    };
    sdk: {
      input: ListPipelineExecutionsCommandInput;
      output: ListPipelineExecutionsCommandOutput;
    };
  };
}
