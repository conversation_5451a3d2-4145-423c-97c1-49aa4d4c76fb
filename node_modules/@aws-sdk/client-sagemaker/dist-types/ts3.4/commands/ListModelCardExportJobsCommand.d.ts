import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListModelCardExportJobsRequest,
  ListModelCardExportJobsResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListModelCardExportJobsCommandInput
  extends ListModelCardExportJobsRequest {}
export interface ListModelCardExportJobsCommandOutput
  extends ListModelCardExportJobsResponse,
    __MetadataBearer {}
declare const ListModelCardExportJobsCommand_base: {
  new (
    input: ListModelCardExportJobsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListModelCardExportJobsCommandInput,
    ListModelCardExportJobsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListModelCardExportJobsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListModelCardExportJobsCommandInput,
    ListModelCardExportJobsCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListModelCardExportJobsCommand extends ListModelCardExportJobsCommand_base {
  protected static __types: {
    api: {
      input: ListModelCardExportJobsRequest;
      output: ListModelCardExportJobsResponse;
    };
    sdk: {
      input: ListModelCardExportJobsCommandInput;
      output: ListModelCardExportJobsCommandOutput;
    };
  };
}
