import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListPipelineParametersForExecutionRequest,
  ListPipelineParametersForExecutionResponse,
} from "../models/models_4";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListPipelineParametersForExecutionCommandInput
  extends ListPipelineParametersForExecutionRequest {}
export interface ListPipelineParametersForExecutionCommandOutput
  extends ListPipelineParametersForExecutionResponse,
    __MetadataBearer {}
declare const ListPipelineParametersForExecutionCommand_base: {
  new (
    input: ListPipelineParametersForExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListPipelineParametersForExecutionCommandInput,
    ListPipelineParametersForExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListPipelineParametersForExecutionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListPipelineParametersForExecutionCommandInput,
    ListPipelineParametersForExecutionCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListPipelineParametersForExecutionCommand extends ListPipelineParametersForExecutionCommand_base {
  protected static __types: {
    api: {
      input: ListPipelineParametersForExecutionRequest;
      output: ListPipelineParametersForExecutionResponse;
    };
    sdk: {
      input: ListPipelineParametersForExecutionCommandInput;
      output: ListPipelineParametersForExecutionCommandOutput;
    };
  };
}
