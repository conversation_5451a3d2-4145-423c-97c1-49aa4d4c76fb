import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListCandidatesForAutoMLJobRequest,
  ListCandidatesForAutoMLJobResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListCandidatesForAutoMLJobCommandInput
  extends ListCandidatesForAutoMLJobRequest {}
export interface ListCandidatesForAutoMLJobCommandOutput
  extends ListCandidatesForAutoMLJobResponse,
    __MetadataBearer {}
declare const ListCandidatesForAutoMLJobCommand_base: {
  new (
    input: ListCandidatesForAutoMLJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListCandidatesForAutoMLJobCommandInput,
    ListCandidatesForAutoMLJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListCandidatesForAutoMLJobCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListCandidatesForAutoMLJobCommandInput,
    ListCandidatesForAutoMLJobCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListCandidatesForAutoMLJobCommand extends ListCandidatesForAutoMLJobCommand_base {
  protected static __types: {
    api: {
      input: ListCandidatesForAutoMLJobRequest;
      output: ListCandidatesForAutoMLJobResponse;
    };
    sdk: {
      input: ListCandidatesForAutoMLJobCommandInput;
      output: ListCandidatesForAutoMLJobCommandOutput;
    };
  };
}
