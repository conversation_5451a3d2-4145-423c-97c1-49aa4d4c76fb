import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ListClusterNodesRequest,
  ListClusterNodesResponse,
} from "../models/models_3";
import {
  SageMakerClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../SageMakerClient";
export { __MetadataBearer };
export { $Command };
export interface ListClusterNodesCommandInput extends ListClusterNodesRequest {}
export interface ListClusterNodesCommandOutput
  extends ListClusterNodesResponse,
    __MetadataBearer {}
declare const ListClusterNodesCommand_base: {
  new (
    input: ListClusterNodesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListClusterNodesCommandInput,
    ListClusterNodesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListClusterNodesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListClusterNodesCommandInput,
    ListClusterNodesCommandOutput,
    SageMakerClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListClusterNodesCommand extends ListClusterNodesCommand_base {
  protected static __types: {
    api: {
      input: ListClusterNodesRequest;
      output: ListClusterNodesResponse;
    };
    sdk: {
      input: ListClusterNodesCommandInput;
      output: ListClusterNodesCommandOutput;
    };
  };
}
