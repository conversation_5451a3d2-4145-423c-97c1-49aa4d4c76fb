import { SageMakerServiceException as __BaseException } from "./SageMakerServiceException";
export const ActionStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
    UNKNOWN: "Unknown",
};
export const ActivationState = {
    DISABLED: "Disabled",
    ENABLED: "Enabled",
};
export const AssociationEdgeType = {
    ASSOCIATED_WITH: "AssociatedWith",
    CONTRIBUTED_TO: "ContributedTo",
    DERIVED_FROM: "DerivedFrom",
    PRODUCED: "Produced",
    SAME_AS: "SameAs",
};
export class ResourceLimitExceeded extends __BaseException {
    name = "ResourceLimitExceeded";
    $fault = "client";
    Message;
    constructor(opts) {
        super({
            name: "ResourceLimitExceeded",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ResourceLimitExceeded.prototype);
        this.Message = opts.Message;
    }
}
export class ResourceNotFound extends __BaseException {
    name = "ResourceNotFound";
    $fault = "client";
    Message;
    constructor(opts) {
        super({
            name: "ResourceNotFound",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ResourceNotFound.prototype);
        this.Message = opts.Message;
    }
}
export const CompressionType = {
    GZIP: "Gzip",
    NONE: "None",
};
export const AdditionalS3DataSourceDataType = {
    S3OBJECT: "S3Object",
    S3PREFIX: "S3Prefix",
};
export const ModelCompressionType = {
    Gzip: "Gzip",
    None: "None",
};
export const S3ModelDataType = {
    S3Object: "S3Object",
    S3Prefix: "S3Prefix",
};
export const ProductionVariantInstanceType = {
    ML_C4_2XLARGE: "ml.c4.2xlarge",
    ML_C4_4XLARGE: "ml.c4.4xlarge",
    ML_C4_8XLARGE: "ml.c4.8xlarge",
    ML_C4_LARGE: "ml.c4.large",
    ML_C4_XLARGE: "ml.c4.xlarge",
    ML_C5D_18XLARGE: "ml.c5d.18xlarge",
    ML_C5D_2XLARGE: "ml.c5d.2xlarge",
    ML_C5D_4XLARGE: "ml.c5d.4xlarge",
    ML_C5D_9XLARGE: "ml.c5d.9xlarge",
    ML_C5D_LARGE: "ml.c5d.large",
    ML_C5D_XLARGE: "ml.c5d.xlarge",
    ML_C5_18XLARGE: "ml.c5.18xlarge",
    ML_C5_2XLARGE: "ml.c5.2xlarge",
    ML_C5_4XLARGE: "ml.c5.4xlarge",
    ML_C5_9XLARGE: "ml.c5.9xlarge",
    ML_C5_LARGE: "ml.c5.large",
    ML_C5_XLARGE: "ml.c5.xlarge",
    ML_C6GD_12XLARGE: "ml.c6gd.12xlarge",
    ML_C6GD_16XLARGE: "ml.c6gd.16xlarge",
    ML_C6GD_2XLARGE: "ml.c6gd.2xlarge",
    ML_C6GD_4XLARGE: "ml.c6gd.4xlarge",
    ML_C6GD_8XLARGE: "ml.c6gd.8xlarge",
    ML_C6GD_LARGE: "ml.c6gd.large",
    ML_C6GD_XLARGE: "ml.c6gd.xlarge",
    ML_C6GN_12XLARGE: "ml.c6gn.12xlarge",
    ML_C6GN_16XLARGE: "ml.c6gn.16xlarge",
    ML_C6GN_2XLARGE: "ml.c6gn.2xlarge",
    ML_C6GN_4XLARGE: "ml.c6gn.4xlarge",
    ML_C6GN_8XLARGE: "ml.c6gn.8xlarge",
    ML_C6GN_LARGE: "ml.c6gn.large",
    ML_C6GN_XLARGE: "ml.c6gn.xlarge",
    ML_C6G_12XLARGE: "ml.c6g.12xlarge",
    ML_C6G_16XLARGE: "ml.c6g.16xlarge",
    ML_C6G_2XLARGE: "ml.c6g.2xlarge",
    ML_C6G_4XLARGE: "ml.c6g.4xlarge",
    ML_C6G_8XLARGE: "ml.c6g.8xlarge",
    ML_C6G_LARGE: "ml.c6g.large",
    ML_C6G_XLARGE: "ml.c6g.xlarge",
    ML_C6I_12XLARGE: "ml.c6i.12xlarge",
    ML_C6I_16XLARGE: "ml.c6i.16xlarge",
    ML_C6I_24XLARGE: "ml.c6i.24xlarge",
    ML_C6I_2XLARGE: "ml.c6i.2xlarge",
    ML_C6I_32XLARGE: "ml.c6i.32xlarge",
    ML_C6I_4XLARGE: "ml.c6i.4xlarge",
    ML_C6I_8XLARGE: "ml.c6i.8xlarge",
    ML_C6I_LARGE: "ml.c6i.large",
    ML_C6I_XLARGE: "ml.c6i.xlarge",
    ML_C7G_12XLARGE: "ml.c7g.12xlarge",
    ML_C7G_16XLARGE: "ml.c7g.16xlarge",
    ML_C7G_2XLARGE: "ml.c7g.2xlarge",
    ML_C7G_4XLARGE: "ml.c7g.4xlarge",
    ML_C7G_8XLARGE: "ml.c7g.8xlarge",
    ML_C7G_LARGE: "ml.c7g.large",
    ML_C7G_XLARGE: "ml.c7g.xlarge",
    ML_C7I_12XLARGE: "ml.c7i.12xlarge",
    ML_C7I_16XLARGE: "ml.c7i.16xlarge",
    ML_C7I_24XLARGE: "ml.c7i.24xlarge",
    ML_C7I_2XLARGE: "ml.c7i.2xlarge",
    ML_C7I_48XLARGE: "ml.c7i.48xlarge",
    ML_C7I_4XLARGE: "ml.c7i.4xlarge",
    ML_C7I_8XLARGE: "ml.c7i.8xlarge",
    ML_C7I_LARGE: "ml.c7i.large",
    ML_C7I_XLARGE: "ml.c7i.xlarge",
    ML_DL1_24XLARGE: "ml.dl1.24xlarge",
    ML_G4DN_12XLARGE: "ml.g4dn.12xlarge",
    ML_G4DN_16XLARGE: "ml.g4dn.16xlarge",
    ML_G4DN_2XLARGE: "ml.g4dn.2xlarge",
    ML_G4DN_4XLARGE: "ml.g4dn.4xlarge",
    ML_G4DN_8XLARGE: "ml.g4dn.8xlarge",
    ML_G4DN_XLARGE: "ml.g4dn.xlarge",
    ML_G5_12XLARGE: "ml.g5.12xlarge",
    ML_G5_16XLARGE: "ml.g5.16xlarge",
    ML_G5_24XLARGE: "ml.g5.24xlarge",
    ML_G5_2XLARGE: "ml.g5.2xlarge",
    ML_G5_48XLARGE: "ml.g5.48xlarge",
    ML_G5_4XLARGE: "ml.g5.4xlarge",
    ML_G5_8XLARGE: "ml.g5.8xlarge",
    ML_G5_XLARGE: "ml.g5.xlarge",
    ML_G6E_12XLARGE: "ml.g6e.12xlarge",
    ML_G6E_16XLARGE: "ml.g6e.16xlarge",
    ML_G6E_24XLARGE: "ml.g6e.24xlarge",
    ML_G6E_2XLARGE: "ml.g6e.2xlarge",
    ML_G6E_48XLARGE: "ml.g6e.48xlarge",
    ML_G6E_4XLARGE: "ml.g6e.4xlarge",
    ML_G6E_8XLARGE: "ml.g6e.8xlarge",
    ML_G6E_XLARGE: "ml.g6e.xlarge",
    ML_G6_12XLARGE: "ml.g6.12xlarge",
    ML_G6_16XLARGE: "ml.g6.16xlarge",
    ML_G6_24XLARGE: "ml.g6.24xlarge",
    ML_G6_2XLARGE: "ml.g6.2xlarge",
    ML_G6_48XLARGE: "ml.g6.48xlarge",
    ML_G6_4XLARGE: "ml.g6.4xlarge",
    ML_G6_8XLARGE: "ml.g6.8xlarge",
    ML_G6_XLARGE: "ml.g6.xlarge",
    ML_INF1_24XLARGE: "ml.inf1.24xlarge",
    ML_INF1_2XLARGE: "ml.inf1.2xlarge",
    ML_INF1_6XLARGE: "ml.inf1.6xlarge",
    ML_INF1_XLARGE: "ml.inf1.xlarge",
    ML_INF2_24XLARGE: "ml.inf2.24xlarge",
    ML_INF2_48XLARGE: "ml.inf2.48xlarge",
    ML_INF2_8XLARGE: "ml.inf2.8xlarge",
    ML_INF2_XLARGE: "ml.inf2.xlarge",
    ML_M4_10XLARGE: "ml.m4.10xlarge",
    ML_M4_16XLARGE: "ml.m4.16xlarge",
    ML_M4_2XLARGE: "ml.m4.2xlarge",
    ML_M4_4XLARGE: "ml.m4.4xlarge",
    ML_M4_XLARGE: "ml.m4.xlarge",
    ML_M5D_12XLARGE: "ml.m5d.12xlarge",
    ML_M5D_24XLARGE: "ml.m5d.24xlarge",
    ML_M5D_2XLARGE: "ml.m5d.2xlarge",
    ML_M5D_4XLARGE: "ml.m5d.4xlarge",
    ML_M5D_LARGE: "ml.m5d.large",
    ML_M5D_XLARGE: "ml.m5d.xlarge",
    ML_M5_12XLARGE: "ml.m5.12xlarge",
    ML_M5_24XLARGE: "ml.m5.24xlarge",
    ML_M5_2XLARGE: "ml.m5.2xlarge",
    ML_M5_4XLARGE: "ml.m5.4xlarge",
    ML_M5_LARGE: "ml.m5.large",
    ML_M5_XLARGE: "ml.m5.xlarge",
    ML_M6GD_12XLARGE: "ml.m6gd.12xlarge",
    ML_M6GD_16XLARGE: "ml.m6gd.16xlarge",
    ML_M6GD_2XLARGE: "ml.m6gd.2xlarge",
    ML_M6GD_4XLARGE: "ml.m6gd.4xlarge",
    ML_M6GD_8XLARGE: "ml.m6gd.8xlarge",
    ML_M6GD_LARGE: "ml.m6gd.large",
    ML_M6GD_XLARGE: "ml.m6gd.xlarge",
    ML_M6G_12XLARGE: "ml.m6g.12xlarge",
    ML_M6G_16XLARGE: "ml.m6g.16xlarge",
    ML_M6G_2XLARGE: "ml.m6g.2xlarge",
    ML_M6G_4XLARGE: "ml.m6g.4xlarge",
    ML_M6G_8XLARGE: "ml.m6g.8xlarge",
    ML_M6G_LARGE: "ml.m6g.large",
    ML_M6G_XLARGE: "ml.m6g.xlarge",
    ML_M6I_12XLARGE: "ml.m6i.12xlarge",
    ML_M6I_16XLARGE: "ml.m6i.16xlarge",
    ML_M6I_24XLARGE: "ml.m6i.24xlarge",
    ML_M6I_2XLARGE: "ml.m6i.2xlarge",
    ML_M6I_32XLARGE: "ml.m6i.32xlarge",
    ML_M6I_4XLARGE: "ml.m6i.4xlarge",
    ML_M6I_8XLARGE: "ml.m6i.8xlarge",
    ML_M6I_LARGE: "ml.m6i.large",
    ML_M6I_XLARGE: "ml.m6i.xlarge",
    ML_M7I_12XLARGE: "ml.m7i.12xlarge",
    ML_M7I_16XLARGE: "ml.m7i.16xlarge",
    ML_M7I_24XLARGE: "ml.m7i.24xlarge",
    ML_M7I_2XLARGE: "ml.m7i.2xlarge",
    ML_M7I_48XLARGE: "ml.m7i.48xlarge",
    ML_M7I_4XLARGE: "ml.m7i.4xlarge",
    ML_M7I_8XLARGE: "ml.m7i.8xlarge",
    ML_M7I_LARGE: "ml.m7i.large",
    ML_M7I_XLARGE: "ml.m7i.xlarge",
    ML_P2_16XLARGE: "ml.p2.16xlarge",
    ML_P2_8XLARGE: "ml.p2.8xlarge",
    ML_P2_XLARGE: "ml.p2.xlarge",
    ML_P3_16XLARGE: "ml.p3.16xlarge",
    ML_P3_2XLARGE: "ml.p3.2xlarge",
    ML_P3_8XLARGE: "ml.p3.8xlarge",
    ML_P4DE_24XLARGE: "ml.p4de.24xlarge",
    ML_P4D_24XLARGE: "ml.p4d.24xlarge",
    ML_P5EN_48XLARGE: "ml.p5en.48xlarge",
    ML_P5E_48XLARGE: "ml.p5e.48xlarge",
    ML_P5_48XLARGE: "ml.p5.48xlarge",
    ML_R5D_12XLARGE: "ml.r5d.12xlarge",
    ML_R5D_24XLARGE: "ml.r5d.24xlarge",
    ML_R5D_2XLARGE: "ml.r5d.2xlarge",
    ML_R5D_4XLARGE: "ml.r5d.4xlarge",
    ML_R5D_LARGE: "ml.r5d.large",
    ML_R5D_XLARGE: "ml.r5d.xlarge",
    ML_R5_12XLARGE: "ml.r5.12xlarge",
    ML_R5_24XLARGE: "ml.r5.24xlarge",
    ML_R5_2XLARGE: "ml.r5.2xlarge",
    ML_R5_4XLARGE: "ml.r5.4xlarge",
    ML_R5_LARGE: "ml.r5.large",
    ML_R5_XLARGE: "ml.r5.xlarge",
    ML_R6GD_12XLARGE: "ml.r6gd.12xlarge",
    ML_R6GD_16XLARGE: "ml.r6gd.16xlarge",
    ML_R6GD_2XLARGE: "ml.r6gd.2xlarge",
    ML_R6GD_4XLARGE: "ml.r6gd.4xlarge",
    ML_R6GD_8XLARGE: "ml.r6gd.8xlarge",
    ML_R6GD_LARGE: "ml.r6gd.large",
    ML_R6GD_XLARGE: "ml.r6gd.xlarge",
    ML_R6G_12XLARGE: "ml.r6g.12xlarge",
    ML_R6G_16XLARGE: "ml.r6g.16xlarge",
    ML_R6G_2XLARGE: "ml.r6g.2xlarge",
    ML_R6G_4XLARGE: "ml.r6g.4xlarge",
    ML_R6G_8XLARGE: "ml.r6g.8xlarge",
    ML_R6G_LARGE: "ml.r6g.large",
    ML_R6G_XLARGE: "ml.r6g.xlarge",
    ML_R6I_12XLARGE: "ml.r6i.12xlarge",
    ML_R6I_16XLARGE: "ml.r6i.16xlarge",
    ML_R6I_24XLARGE: "ml.r6i.24xlarge",
    ML_R6I_2XLARGE: "ml.r6i.2xlarge",
    ML_R6I_32XLARGE: "ml.r6i.32xlarge",
    ML_R6I_4XLARGE: "ml.r6i.4xlarge",
    ML_R6I_8XLARGE: "ml.r6i.8xlarge",
    ML_R6I_LARGE: "ml.r6i.large",
    ML_R6I_XLARGE: "ml.r6i.xlarge",
    ML_R7I_12XLARGE: "ml.r7i.12xlarge",
    ML_R7I_16XLARGE: "ml.r7i.16xlarge",
    ML_R7I_24XLARGE: "ml.r7i.24xlarge",
    ML_R7I_2XLARGE: "ml.r7i.2xlarge",
    ML_R7I_48XLARGE: "ml.r7i.48xlarge",
    ML_R7I_4XLARGE: "ml.r7i.4xlarge",
    ML_R7I_8XLARGE: "ml.r7i.8xlarge",
    ML_R7I_LARGE: "ml.r7i.large",
    ML_R7I_XLARGE: "ml.r7i.xlarge",
    ML_R8G_12XLARGE: "ml.r8g.12xlarge",
    ML_R8G_16XLARGE: "ml.r8g.16xlarge",
    ML_R8G_24XLARGE: "ml.r8g.24xlarge",
    ML_R8G_2XLARGE: "ml.r8g.2xlarge",
    ML_R8G_48XLARGE: "ml.r8g.48xlarge",
    ML_R8G_4XLARGE: "ml.r8g.4xlarge",
    ML_R8G_8XLARGE: "ml.r8g.8xlarge",
    ML_R8G_LARGE: "ml.r8g.large",
    ML_R8G_MEDIUM: "ml.r8g.medium",
    ML_R8G_XLARGE: "ml.r8g.xlarge",
    ML_T2_2XLARGE: "ml.t2.2xlarge",
    ML_T2_LARGE: "ml.t2.large",
    ML_T2_MEDIUM: "ml.t2.medium",
    ML_T2_XLARGE: "ml.t2.xlarge",
    ML_TRN1N_32XLARGE: "ml.trn1n.32xlarge",
    ML_TRN1_2XLARGE: "ml.trn1.2xlarge",
    ML_TRN1_32XLARGE: "ml.trn1.32xlarge",
    ML_TRN2_48XLARGE: "ml.trn2.48xlarge",
};
export const TransformInstanceType = {
    ML_C4_2XLARGE: "ml.c4.2xlarge",
    ML_C4_4XLARGE: "ml.c4.4xlarge",
    ML_C4_8XLARGE: "ml.c4.8xlarge",
    ML_C4_XLARGE: "ml.c4.xlarge",
    ML_C5_18XLARGE: "ml.c5.18xlarge",
    ML_C5_2XLARGE: "ml.c5.2xlarge",
    ML_C5_4XLARGE: "ml.c5.4xlarge",
    ML_C5_9XLARGE: "ml.c5.9xlarge",
    ML_C5_XLARGE: "ml.c5.xlarge",
    ML_C6I_12XLARGE: "ml.c6i.12xlarge",
    ML_C6I_16XLARGE: "ml.c6i.16xlarge",
    ML_C6I_24XLARGE: "ml.c6i.24xlarge",
    ML_C6I_2XLARGE: "ml.c6i.2xlarge",
    ML_C6I_32XLARGE: "ml.c6i.32xlarge",
    ML_C6I_4XLARGE: "ml.c6i.4xlarge",
    ML_C6I_8XLARGE: "ml.c6i.8xlarge",
    ML_C6I_LARGE: "ml.c6i.large",
    ML_C6I_XLARGE: "ml.c6i.xlarge",
    ML_C7I_12XLARGE: "ml.c7i.12xlarge",
    ML_C7I_16XLARGE: "ml.c7i.16xlarge",
    ML_C7I_24XLARGE: "ml.c7i.24xlarge",
    ML_C7I_2XLARGE: "ml.c7i.2xlarge",
    ML_C7I_48XLARGE: "ml.c7i.48xlarge",
    ML_C7I_4XLARGE: "ml.c7i.4xlarge",
    ML_C7I_8XLARGE: "ml.c7i.8xlarge",
    ML_C7I_LARGE: "ml.c7i.large",
    ML_C7I_XLARGE: "ml.c7i.xlarge",
    ML_G4DN_12XLARGE: "ml.g4dn.12xlarge",
    ML_G4DN_16XLARGE: "ml.g4dn.16xlarge",
    ML_G4DN_2XLARGE: "ml.g4dn.2xlarge",
    ML_G4DN_4XLARGE: "ml.g4dn.4xlarge",
    ML_G4DN_8XLARGE: "ml.g4dn.8xlarge",
    ML_G4DN_XLARGE: "ml.g4dn.xlarge",
    ML_G5_12XLARGE: "ml.g5.12xlarge",
    ML_G5_16XLARGE: "ml.g5.16xlarge",
    ML_G5_24XLARGE: "ml.g5.24xlarge",
    ML_G5_2XLARGE: "ml.g5.2xlarge",
    ML_G5_48XLARGE: "ml.g5.48xlarge",
    ML_G5_4XLARGE: "ml.g5.4xlarge",
    ML_G5_8XLARGE: "ml.g5.8xlarge",
    ML_G5_XLARGE: "ml.g5.xlarge",
    ML_INF2_24XLARGE: "ml.inf2.24xlarge",
    ML_INF2_48XLARGE: "ml.inf2.48xlarge",
    ML_INF2_8XLARGE: "ml.inf2.8xlarge",
    ML_INF2_XLARGE: "ml.inf2.xlarge",
    ML_M4_10XLARGE: "ml.m4.10xlarge",
    ML_M4_16XLARGE: "ml.m4.16xlarge",
    ML_M4_2XLARGE: "ml.m4.2xlarge",
    ML_M4_4XLARGE: "ml.m4.4xlarge",
    ML_M4_XLARGE: "ml.m4.xlarge",
    ML_M5_12XLARGE: "ml.m5.12xlarge",
    ML_M5_24XLARGE: "ml.m5.24xlarge",
    ML_M5_2XLARGE: "ml.m5.2xlarge",
    ML_M5_4XLARGE: "ml.m5.4xlarge",
    ML_M5_LARGE: "ml.m5.large",
    ML_M5_XLARGE: "ml.m5.xlarge",
    ML_M6I_12XLARGE: "ml.m6i.12xlarge",
    ML_M6I_16XLARGE: "ml.m6i.16xlarge",
    ML_M6I_24XLARGE: "ml.m6i.24xlarge",
    ML_M6I_2XLARGE: "ml.m6i.2xlarge",
    ML_M6I_32XLARGE: "ml.m6i.32xlarge",
    ML_M6I_4XLARGE: "ml.m6i.4xlarge",
    ML_M6I_8XLARGE: "ml.m6i.8xlarge",
    ML_M6I_LARGE: "ml.m6i.large",
    ML_M6I_XLARGE: "ml.m6i.xlarge",
    ML_M7I_12XLARGE: "ml.m7i.12xlarge",
    ML_M7I_16XLARGE: "ml.m7i.16xlarge",
    ML_M7I_24XLARGE: "ml.m7i.24xlarge",
    ML_M7I_2XLARGE: "ml.m7i.2xlarge",
    ML_M7I_48XLARGE: "ml.m7i.48xlarge",
    ML_M7I_4XLARGE: "ml.m7i.4xlarge",
    ML_M7I_8XLARGE: "ml.m7i.8xlarge",
    ML_M7I_LARGE: "ml.m7i.large",
    ML_M7I_XLARGE: "ml.m7i.xlarge",
    ML_P2_16XLARGE: "ml.p2.16xlarge",
    ML_P2_8XLARGE: "ml.p2.8xlarge",
    ML_P2_XLARGE: "ml.p2.xlarge",
    ML_P3_16XLARGE: "ml.p3.16xlarge",
    ML_P3_2XLARGE: "ml.p3.2xlarge",
    ML_P3_8XLARGE: "ml.p3.8xlarge",
    ML_R6I_12XLARGE: "ml.r6i.12xlarge",
    ML_R6I_16XLARGE: "ml.r6i.16xlarge",
    ML_R6I_24XLARGE: "ml.r6i.24xlarge",
    ML_R6I_2XLARGE: "ml.r6i.2xlarge",
    ML_R6I_32XLARGE: "ml.r6i.32xlarge",
    ML_R6I_4XLARGE: "ml.r6i.4xlarge",
    ML_R6I_8XLARGE: "ml.r6i.8xlarge",
    ML_R6I_LARGE: "ml.r6i.large",
    ML_R6I_XLARGE: "ml.r6i.xlarge",
    ML_R7I_12XLARGE: "ml.r7i.12xlarge",
    ML_R7I_16XLARGE: "ml.r7i.16xlarge",
    ML_R7I_24XLARGE: "ml.r7i.24xlarge",
    ML_R7I_2XLARGE: "ml.r7i.2xlarge",
    ML_R7I_48XLARGE: "ml.r7i.48xlarge",
    ML_R7I_4XLARGE: "ml.r7i.4xlarge",
    ML_R7I_8XLARGE: "ml.r7i.8xlarge",
    ML_R7I_LARGE: "ml.r7i.large",
    ML_R7I_XLARGE: "ml.r7i.xlarge",
    ML_TRN1_2XLARGE: "ml.trn1.2xlarge",
    ML_TRN1_32XLARGE: "ml.trn1.32xlarge",
};
export const AggregationTransformationValue = {
    Avg: "avg",
    First: "first",
    Max: "max",
    Min: "min",
    Sum: "sum",
};
export const AlgorithmSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const TrainingRepositoryAccessMode = {
    PLATFORM: "Platform",
    VPC: "Vpc",
};
export const TrainingInputMode = {
    FASTFILE: "FastFile",
    FILE: "File",
    PIPE: "Pipe",
};
export const AlgorithmStatus = {
    COMPLETED: "Completed",
    DELETING: "Deleting",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    PENDING: "Pending",
};
export const DetailedAlgorithmStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    NOT_STARTED: "NotStarted",
};
export const FileSystemAccessMode = {
    RO: "ro",
    RW: "rw",
};
export const FileSystemType = {
    EFS: "EFS",
    FSXLUSTRE: "FSxLustre",
};
export const S3DataDistribution = {
    FULLY_REPLICATED: "FullyReplicated",
    SHARDED_BY_S3_KEY: "ShardedByS3Key",
};
export const S3DataType = {
    AUGMENTED_MANIFEST_FILE: "AugmentedManifestFile",
    MANIFEST_FILE: "ManifestFile",
    S3_PREFIX: "S3Prefix",
};
export const RecordWrapper = {
    NONE: "None",
    RECORDIO: "RecordIO",
};
export const OutputCompressionType = {
    GZIP: "GZIP",
    NONE: "NONE",
};
export const TrainingInstanceType = {
    ML_C4_2XLARGE: "ml.c4.2xlarge",
    ML_C4_4XLARGE: "ml.c4.4xlarge",
    ML_C4_8XLARGE: "ml.c4.8xlarge",
    ML_C4_XLARGE: "ml.c4.xlarge",
    ML_C5N_18XLARGE: "ml.c5n.18xlarge",
    ML_C5N_2XLARGE: "ml.c5n.2xlarge",
    ML_C5N_4XLARGE: "ml.c5n.4xlarge",
    ML_C5N_9XLARGE: "ml.c5n.9xlarge",
    ML_C5N_XLARGE: "ml.c5n.xlarge",
    ML_C5_18XLARGE: "ml.c5.18xlarge",
    ML_C5_2XLARGE: "ml.c5.2xlarge",
    ML_C5_4XLARGE: "ml.c5.4xlarge",
    ML_C5_9XLARGE: "ml.c5.9xlarge",
    ML_C5_XLARGE: "ml.c5.xlarge",
    ML_C6I_12XLARGE: "ml.c6i.12xlarge",
    ML_C6I_16XLARGE: "ml.c6i.16xlarge",
    ML_C6I_24XLARGE: "ml.c6i.24xlarge",
    ML_C6I_2XLARGE: "ml.c6i.2xlarge",
    ML_C6I_32XLARGE: "ml.c6i.32xlarge",
    ML_C6I_4XLARGE: "ml.c6i.4xlarge",
    ML_C6I_8XLARGE: "ml.c6i.8xlarge",
    ML_C6I_XLARGE: "ml.c6i.xlarge",
    ML_G4DN_12XLARGE: "ml.g4dn.12xlarge",
    ML_G4DN_16XLARGE: "ml.g4dn.16xlarge",
    ML_G4DN_2XLARGE: "ml.g4dn.2xlarge",
    ML_G4DN_4XLARGE: "ml.g4dn.4xlarge",
    ML_G4DN_8XLARGE: "ml.g4dn.8xlarge",
    ML_G4DN_XLARGE: "ml.g4dn.xlarge",
    ML_G5_12XLARGE: "ml.g5.12xlarge",
    ML_G5_16XLARGE: "ml.g5.16xlarge",
    ML_G5_24XLARGE: "ml.g5.24xlarge",
    ML_G5_2XLARGE: "ml.g5.2xlarge",
    ML_G5_48XLARGE: "ml.g5.48xlarge",
    ML_G5_4XLARGE: "ml.g5.4xlarge",
    ML_G5_8XLARGE: "ml.g5.8xlarge",
    ML_G5_XLARGE: "ml.g5.xlarge",
    ML_G6E_12XLARGE: "ml.g6e.12xlarge",
    ML_G6E_16XLARGE: "ml.g6e.16xlarge",
    ML_G6E_24XLARGE: "ml.g6e.24xlarge",
    ML_G6E_2XLARGE: "ml.g6e.2xlarge",
    ML_G6E_48XLARGE: "ml.g6e.48xlarge",
    ML_G6E_4XLARGE: "ml.g6e.4xlarge",
    ML_G6E_8XLARGE: "ml.g6e.8xlarge",
    ML_G6E_XLARGE: "ml.g6e.xlarge",
    ML_G6_12XLARGE: "ml.g6.12xlarge",
    ML_G6_16XLARGE: "ml.g6.16xlarge",
    ML_G6_24XLARGE: "ml.g6.24xlarge",
    ML_G6_2XLARGE: "ml.g6.2xlarge",
    ML_G6_48XLARGE: "ml.g6.48xlarge",
    ML_G6_4XLARGE: "ml.g6.4xlarge",
    ML_G6_8XLARGE: "ml.g6.8xlarge",
    ML_G6_XLARGE: "ml.g6.xlarge",
    ML_M4_10XLARGE: "ml.m4.10xlarge",
    ML_M4_16XLARGE: "ml.m4.16xlarge",
    ML_M4_2XLARGE: "ml.m4.2xlarge",
    ML_M4_4XLARGE: "ml.m4.4xlarge",
    ML_M4_XLARGE: "ml.m4.xlarge",
    ML_M5_12XLARGE: "ml.m5.12xlarge",
    ML_M5_24XLARGE: "ml.m5.24xlarge",
    ML_M5_2XLARGE: "ml.m5.2xlarge",
    ML_M5_4XLARGE: "ml.m5.4xlarge",
    ML_M5_LARGE: "ml.m5.large",
    ML_M5_XLARGE: "ml.m5.xlarge",
    ML_M6I_12XLARGE: "ml.m6i.12xlarge",
    ML_M6I_16XLARGE: "ml.m6i.16xlarge",
    ML_M6I_24XLARGE: "ml.m6i.24xlarge",
    ML_M6I_2XLARGE: "ml.m6i.2xlarge",
    ML_M6I_32XLARGE: "ml.m6i.32xlarge",
    ML_M6I_4XLARGE: "ml.m6i.4xlarge",
    ML_M6I_8XLARGE: "ml.m6i.8xlarge",
    ML_M6I_LARGE: "ml.m6i.large",
    ML_M6I_XLARGE: "ml.m6i.xlarge",
    ML_P2_16XLARGE: "ml.p2.16xlarge",
    ML_P2_8XLARGE: "ml.p2.8xlarge",
    ML_P2_XLARGE: "ml.p2.xlarge",
    ML_P3DN_24XLARGE: "ml.p3dn.24xlarge",
    ML_P3_16XLARGE: "ml.p3.16xlarge",
    ML_P3_2XLARGE: "ml.p3.2xlarge",
    ML_P3_8XLARGE: "ml.p3.8xlarge",
    ML_P4DE_24XLARGE: "ml.p4de.24xlarge",
    ML_P4D_24XLARGE: "ml.p4d.24xlarge",
    ML_P5EN_48XLARGE: "ml.p5en.48xlarge",
    ML_P5E_48XLARGE: "ml.p5e.48xlarge",
    ML_P5_48XLARGE: "ml.p5.48xlarge",
    ML_R5D_12XLARGE: "ml.r5d.12xlarge",
    ML_R5D_16XLARGE: "ml.r5d.16xlarge",
    ML_R5D_24XLARGE: "ml.r5d.24xlarge",
    ML_R5D_2XLARGE: "ml.r5d.2xlarge",
    ML_R5D_4XLARGE: "ml.r5d.4xlarge",
    ML_R5D_8XLARGE: "ml.r5d.8xlarge",
    ML_R5D_LARGE: "ml.r5d.large",
    ML_R5D_XLARGE: "ml.r5d.xlarge",
    ML_R5_12XLARGE: "ml.r5.12xlarge",
    ML_R5_16XLARGE: "ml.r5.16xlarge",
    ML_R5_24XLARGE: "ml.r5.24xlarge",
    ML_R5_2XLARGE: "ml.r5.2xlarge",
    ML_R5_4XLARGE: "ml.r5.4xlarge",
    ML_R5_8XLARGE: "ml.r5.8xlarge",
    ML_R5_LARGE: "ml.r5.large",
    ML_R5_XLARGE: "ml.r5.xlarge",
    ML_T3_2XLARGE: "ml.t3.2xlarge",
    ML_T3_LARGE: "ml.t3.large",
    ML_T3_MEDIUM: "ml.t3.medium",
    ML_T3_XLARGE: "ml.t3.xlarge",
    ML_TRN1N_32XLARGE: "ml.trn1n.32xlarge",
    ML_TRN1_2XLARGE: "ml.trn1.2xlarge",
    ML_TRN1_32XLARGE: "ml.trn1.32xlarge",
    ML_TRN2_48XLARGE: "ml.trn2.48xlarge",
};
export const BatchStrategy = {
    MULTI_RECORD: "MultiRecord",
    SINGLE_RECORD: "SingleRecord",
};
export const SplitType = {
    LINE: "Line",
    NONE: "None",
    RECORDIO: "RecordIO",
    TFRECORD: "TFRecord",
};
export const AssemblyType = {
    LINE: "Line",
    NONE: "None",
};
export const FeatureStatus = {
    Disabled: "DISABLED",
    Enabled: "ENABLED",
};
export const AppType = {
    Canvas: "Canvas",
    CodeEditor: "CodeEditor",
    DetailedProfiler: "DetailedProfiler",
    JupyterLab: "JupyterLab",
    JupyterServer: "JupyterServer",
    KernelGateway: "KernelGateway",
    RSessionGateway: "RSessionGateway",
    RStudioServerPro: "RStudioServerPro",
    TensorBoard: "TensorBoard",
};
export const AppInstanceType = {
    ML_C5_12XLARGE: "ml.c5.12xlarge",
    ML_C5_18XLARGE: "ml.c5.18xlarge",
    ML_C5_24XLARGE: "ml.c5.24xlarge",
    ML_C5_2XLARGE: "ml.c5.2xlarge",
    ML_C5_4XLARGE: "ml.c5.4xlarge",
    ML_C5_9XLARGE: "ml.c5.9xlarge",
    ML_C5_LARGE: "ml.c5.large",
    ML_C5_XLARGE: "ml.c5.xlarge",
    ML_C6ID_12XLARGE: "ml.c6id.12xlarge",
    ML_C6ID_16XLARGE: "ml.c6id.16xlarge",
    ML_C6ID_24XLARGE: "ml.c6id.24xlarge",
    ML_C6ID_2XLARGE: "ml.c6id.2xlarge",
    ML_C6ID_32XLARGE: "ml.c6id.32xlarge",
    ML_C6ID_4XLARGE: "ml.c6id.4xlarge",
    ML_C6ID_8XLARGE: "ml.c6id.8xlarge",
    ML_C6ID_LARGE: "ml.c6id.large",
    ML_C6ID_XLARGE: "ml.c6id.xlarge",
    ML_C6I_12XLARGE: "ml.c6i.12xlarge",
    ML_C6I_16XLARGE: "ml.c6i.16xlarge",
    ML_C6I_24XLARGE: "ml.c6i.24xlarge",
    ML_C6I_2XLARGE: "ml.c6i.2xlarge",
    ML_C6I_32XLARGE: "ml.c6i.32xlarge",
    ML_C6I_4XLARGE: "ml.c6i.4xlarge",
    ML_C6I_8XLARGE: "ml.c6i.8xlarge",
    ML_C6I_LARGE: "ml.c6i.large",
    ML_C6I_XLARGE: "ml.c6i.xlarge",
    ML_C7I_12XLARGE: "ml.c7i.12xlarge",
    ML_C7I_16XLARGE: "ml.c7i.16xlarge",
    ML_C7I_24XLARGE: "ml.c7i.24xlarge",
    ML_C7I_2XLARGE: "ml.c7i.2xlarge",
    ML_C7I_48XLARGE: "ml.c7i.48xlarge",
    ML_C7I_4XLARGE: "ml.c7i.4xlarge",
    ML_C7I_8XLARGE: "ml.c7i.8xlarge",
    ML_C7I_LARGE: "ml.c7i.large",
    ML_C7I_XLARGE: "ml.c7i.xlarge",
    ML_G4DN_12XLARGE: "ml.g4dn.12xlarge",
    ML_G4DN_16XLARGE: "ml.g4dn.16xlarge",
    ML_G4DN_2XLARGE: "ml.g4dn.2xlarge",
    ML_G4DN_4XLARGE: "ml.g4dn.4xlarge",
    ML_G4DN_8XLARGE: "ml.g4dn.8xlarge",
    ML_G4DN_XLARGE: "ml.g4dn.xlarge",
    ML_G5_12XLARGE: "ml.g5.12xlarge",
    ML_G5_16XLARGE: "ml.g5.16xlarge",
    ML_G5_24XLARGE: "ml.g5.24xlarge",
    ML_G5_2XLARGE: "ml.g5.2xlarge",
    ML_G5_48XLARGE: "ml.g5.48xlarge",
    ML_G5_4XLARGE: "ml.g5.4xlarge",
    ML_G5_8XLARGE: "ml.g5.8xlarge",
    ML_G5_XLARGE: "ml.g5.xlarge",
    ML_G6E_12XLARGE: "ml.g6e.12xlarge",
    ML_G6E_16XLARGE: "ml.g6e.16xlarge",
    ML_G6E_24XLARGE: "ml.g6e.24xlarge",
    ML_G6E_2XLARGE: "ml.g6e.2xlarge",
    ML_G6E_48XLARGE: "ml.g6e.48xlarge",
    ML_G6E_4XLARGE: "ml.g6e.4xlarge",
    ML_G6E_8XLARGE: "ml.g6e.8xlarge",
    ML_G6E_XLARGE: "ml.g6e.xlarge",
    ML_G6_12XLARGE: "ml.g6.12xlarge",
    ML_G6_16XLARGE: "ml.g6.16xlarge",
    ML_G6_24XLARGE: "ml.g6.24xlarge",
    ML_G6_2XLARGE: "ml.g6.2xlarge",
    ML_G6_48XLARGE: "ml.g6.48xlarge",
    ML_G6_4XLARGE: "ml.g6.4xlarge",
    ML_G6_8XLARGE: "ml.g6.8xlarge",
    ML_G6_XLARGE: "ml.g6.xlarge",
    ML_GEOSPATIAL_INTERACTIVE: "ml.geospatial.interactive",
    ML_M5D_12XLARGE: "ml.m5d.12xlarge",
    ML_M5D_16XLARGE: "ml.m5d.16xlarge",
    ML_M5D_24XLARGE: "ml.m5d.24xlarge",
    ML_M5D_2XLARGE: "ml.m5d.2xlarge",
    ML_M5D_4XLARGE: "ml.m5d.4xlarge",
    ML_M5D_8XLARGE: "ml.m5d.8xlarge",
    ML_M5D_LARGE: "ml.m5d.large",
    ML_M5D_XLARGE: "ml.m5d.xlarge",
    ML_M5_12XLARGE: "ml.m5.12xlarge",
    ML_M5_16XLARGE: "ml.m5.16xlarge",
    ML_M5_24XLARGE: "ml.m5.24xlarge",
    ML_M5_2XLARGE: "ml.m5.2xlarge",
    ML_M5_4XLARGE: "ml.m5.4xlarge",
    ML_M5_8XLARGE: "ml.m5.8xlarge",
    ML_M5_LARGE: "ml.m5.large",
    ML_M5_XLARGE: "ml.m5.xlarge",
    ML_M6ID_12XLARGE: "ml.m6id.12xlarge",
    ML_M6ID_16XLARGE: "ml.m6id.16xlarge",
    ML_M6ID_24XLARGE: "ml.m6id.24xlarge",
    ML_M6ID_2XLARGE: "ml.m6id.2xlarge",
    ML_M6ID_32XLARGE: "ml.m6id.32xlarge",
    ML_M6ID_4XLARGE: "ml.m6id.4xlarge",
    ML_M6ID_8XLARGE: "ml.m6id.8xlarge",
    ML_M6ID_LARGE: "ml.m6id.large",
    ML_M6ID_XLARGE: "ml.m6id.xlarge",
    ML_M6I_12XLARGE: "ml.m6i.12xlarge",
    ML_M6I_16XLARGE: "ml.m6i.16xlarge",
    ML_M6I_24XLARGE: "ml.m6i.24xlarge",
    ML_M6I_2XLARGE: "ml.m6i.2xlarge",
    ML_M6I_32XLARGE: "ml.m6i.32xlarge",
    ML_M6I_4XLARGE: "ml.m6i.4xlarge",
    ML_M6I_8XLARGE: "ml.m6i.8xlarge",
    ML_M6I_LARGE: "ml.m6i.large",
    ML_M6I_XLARGE: "ml.m6i.xlarge",
    ML_M7I_12XLARGE: "ml.m7i.12xlarge",
    ML_M7I_16XLARGE: "ml.m7i.16xlarge",
    ML_M7I_24XLARGE: "ml.m7i.24xlarge",
    ML_M7I_2XLARGE: "ml.m7i.2xlarge",
    ML_M7I_48XLARGE: "ml.m7i.48xlarge",
    ML_M7I_4XLARGE: "ml.m7i.4xlarge",
    ML_M7I_8XLARGE: "ml.m7i.8xlarge",
    ML_M7I_LARGE: "ml.m7i.large",
    ML_M7I_XLARGE: "ml.m7i.xlarge",
    ML_P3DN_24XLARGE: "ml.p3dn.24xlarge",
    ML_P3_16XLARGE: "ml.p3.16xlarge",
    ML_P3_2XLARGE: "ml.p3.2xlarge",
    ML_P3_8XLARGE: "ml.p3.8xlarge",
    ML_P4DE_24XLARGE: "ml.p4de.24xlarge",
    ML_P4D_24XLARGE: "ml.p4d.24xlarge",
    ML_P5EN_48XLARGE: "ml.p5en.48xlarge",
    ML_P5_48XLARGE: "ml.p5.48xlarge",
    ML_R5_12XLARGE: "ml.r5.12xlarge",
    ML_R5_16XLARGE: "ml.r5.16xlarge",
    ML_R5_24XLARGE: "ml.r5.24xlarge",
    ML_R5_2XLARGE: "ml.r5.2xlarge",
    ML_R5_4XLARGE: "ml.r5.4xlarge",
    ML_R5_8XLARGE: "ml.r5.8xlarge",
    ML_R5_LARGE: "ml.r5.large",
    ML_R5_XLARGE: "ml.r5.xlarge",
    ML_R6ID_12XLARGE: "ml.r6id.12xlarge",
    ML_R6ID_16XLARGE: "ml.r6id.16xlarge",
    ML_R6ID_24XLARGE: "ml.r6id.24xlarge",
    ML_R6ID_2XLARGE: "ml.r6id.2xlarge",
    ML_R6ID_32XLARGE: "ml.r6id.32xlarge",
    ML_R6ID_4XLARGE: "ml.r6id.4xlarge",
    ML_R6ID_8XLARGE: "ml.r6id.8xlarge",
    ML_R6ID_LARGE: "ml.r6id.large",
    ML_R6ID_XLARGE: "ml.r6id.xlarge",
    ML_R6I_12XLARGE: "ml.r6i.12xlarge",
    ML_R6I_16XLARGE: "ml.r6i.16xlarge",
    ML_R6I_24XLARGE: "ml.r6i.24xlarge",
    ML_R6I_2XLARGE: "ml.r6i.2xlarge",
    ML_R6I_32XLARGE: "ml.r6i.32xlarge",
    ML_R6I_4XLARGE: "ml.r6i.4xlarge",
    ML_R6I_8XLARGE: "ml.r6i.8xlarge",
    ML_R6I_LARGE: "ml.r6i.large",
    ML_R6I_XLARGE: "ml.r6i.xlarge",
    ML_R7I_12XLARGE: "ml.r7i.12xlarge",
    ML_R7I_16XLARGE: "ml.r7i.16xlarge",
    ML_R7I_24XLARGE: "ml.r7i.24xlarge",
    ML_R7I_2XLARGE: "ml.r7i.2xlarge",
    ML_R7I_48XLARGE: "ml.r7i.48xlarge",
    ML_R7I_4XLARGE: "ml.r7i.4xlarge",
    ML_R7I_8XLARGE: "ml.r7i.8xlarge",
    ML_R7I_LARGE: "ml.r7i.large",
    ML_R7I_XLARGE: "ml.r7i.xlarge",
    ML_T3_2XLARGE: "ml.t3.2xlarge",
    ML_T3_LARGE: "ml.t3.large",
    ML_T3_MEDIUM: "ml.t3.medium",
    ML_T3_MICRO: "ml.t3.micro",
    ML_T3_SMALL: "ml.t3.small",
    ML_T3_XLARGE: "ml.t3.xlarge",
    ML_TRN1N_32XLARGE: "ml.trn1n.32xlarge",
    ML_TRN1_2XLARGE: "ml.trn1.2xlarge",
    ML_TRN1_32XLARGE: "ml.trn1.32xlarge",
    SYSTEM: "system",
};
export const AppStatus = {
    Deleted: "Deleted",
    Deleting: "Deleting",
    Failed: "Failed",
    InService: "InService",
    Pending: "Pending",
};
export const AppImageConfigSortKey = {
    CreationTime: "CreationTime",
    LastModifiedTime: "LastModifiedTime",
    Name: "Name",
};
export const LifecycleManagement = {
    Disabled: "DISABLED",
    Enabled: "ENABLED",
};
export const AppNetworkAccessType = {
    PublicInternetOnly: "PublicInternetOnly",
    VpcOnly: "VpcOnly",
};
export const AppSecurityGroupManagement = {
    Customer: "Customer",
    Service: "Service",
};
export const AppSortKey = {
    CreationTime: "CreationTime",
};
export const ArtifactSourceIdType = {
    CUSTOM: "Custom",
    MD5_HASH: "MD5Hash",
    S3_ETAG: "S3ETag",
    S3_VERSION: "S3Version",
};
export const AsyncNotificationTopicTypes = {
    ERROR_NOTIFICATION_TOPIC: "ERROR_NOTIFICATION_TOPIC",
    SUCCESS_NOTIFICATION_TOPIC: "SUCCESS_NOTIFICATION_TOPIC",
};
export const AthenaResultCompressionType = {
    GZIP: "GZIP",
    SNAPPY: "SNAPPY",
    ZLIB: "ZLIB",
};
export const AthenaResultFormat = {
    AVRO: "AVRO",
    JSON: "JSON",
    ORC: "ORC",
    PARQUET: "PARQUET",
    TEXTFILE: "TEXTFILE",
};
export const AuthMode = {
    IAM: "IAM",
    SSO: "SSO",
};
export const AutoMLAlgorithm = {
    ARIMA: "arima",
    CATBOOST: "catboost",
    CNN_QR: "cnn-qr",
    DEEPAR: "deepar",
    ETS: "ets",
    EXTRA_TREES: "extra-trees",
    FASTAI: "fastai",
    LIGHTGBM: "lightgbm",
    LINEAR_LEARNER: "linear-learner",
    MLP: "mlp",
    NN_TORCH: "nn-torch",
    NPTS: "npts",
    PROPHET: "prophet",
    RANDOMFOREST: "randomforest",
    XGBOOST: "xgboost",
};
export const AutoMLMetricEnum = {
    ACCURACY: "Accuracy",
    AUC: "AUC",
    AVERAGE_WEIGHTED_QUANTILE_LOSS: "AverageWeightedQuantileLoss",
    BALANCED_ACCURACY: "BalancedAccuracy",
    F1: "F1",
    F1_MACRO: "F1macro",
    MAE: "MAE",
    MAPE: "MAPE",
    MASE: "MASE",
    MSE: "MSE",
    PRECISION: "Precision",
    PRECISION_MACRO: "PrecisionMacro",
    R2: "R2",
    RECALL: "Recall",
    RECALL_MACRO: "RecallMacro",
    RMSE: "RMSE",
    WAPE: "WAPE",
};
export const MetricSetSource = {
    TEST: "Test",
    TRAIN: "Train",
    VALIDATION: "Validation",
};
export const AutoMLMetricExtendedEnum = {
    ACCURACY: "Accuracy",
    AUC: "AUC",
    AVERAGE_WEIGHTED_QUANTILE_LOSS: "AverageWeightedQuantileLoss",
    BALANCED_ACCURACY: "BalancedAccuracy",
    F1: "F1",
    F1_MACRO: "F1macro",
    INFERENCE_LATENCY: "InferenceLatency",
    LogLoss: "LogLoss",
    MAE: "MAE",
    MAPE: "MAPE",
    MASE: "MASE",
    MSE: "MSE",
    PERPLEXITY: "Perplexity",
    PRECISION: "Precision",
    PRECISION_MACRO: "PrecisionMacro",
    R2: "R2",
    RECALL: "Recall",
    RECALL_MACRO: "RecallMacro",
    RMSE: "RMSE",
    ROUGE1: "Rouge1",
    ROUGE2: "Rouge2",
    ROUGEL: "RougeL",
    ROUGEL_SUM: "RougeLSum",
    TRAINING_LOSS: "TrainingLoss",
    VALIDATION_LOSS: "ValidationLoss",
    WAPE: "WAPE",
};
export const CandidateStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
};
export const CandidateStepType = {
    PROCESSING: "AWS::SageMaker::ProcessingJob",
    TRAINING: "AWS::SageMaker::TrainingJob",
    TRANSFORM: "AWS::SageMaker::TransformJob",
};
export const AutoMLJobObjectiveType = {
    MAXIMIZE: "Maximize",
    MINIMIZE: "Minimize",
};
export const AutoMLProcessingUnit = {
    CPU: "CPU",
    GPU: "GPU",
};
export const ObjectiveStatus = {
    Failed: "Failed",
    Pending: "Pending",
    Succeeded: "Succeeded",
};
export const AutoMLChannelType = {
    TRAINING: "training",
    VALIDATION: "validation",
};
export const AutoMLS3DataType = {
    AUGMENTED_MANIFEST_FILE: "AugmentedManifestFile",
    MANIFEST_FILE: "ManifestFile",
    S3_PREFIX: "S3Prefix",
};
export const AutoMLMode = {
    AUTO: "AUTO",
    ENSEMBLING: "ENSEMBLING",
    HYPERPARAMETER_TUNING: "HYPERPARAMETER_TUNING",
};
export const AutoMLJobSecondaryStatus = {
    ANALYZING_DATA: "AnalyzingData",
    CANDIDATE_DEFINITIONS_GENERATED: "CandidateDefinitionsGenerated",
    COMPLETED: "Completed",
    DEPLOYING_MODEL: "DeployingModel",
    EXPLAINABILITY_ERROR: "ExplainabilityError",
    FAILED: "Failed",
    FEATURE_ENGINEERING: "FeatureEngineering",
    GENERATING_EXPLAINABILITY_REPORT: "GeneratingExplainabilityReport",
    GENERATING_MODEL_INSIGHTS_REPORT: "GeneratingModelInsightsReport",
    MAX_AUTO_ML_JOB_RUNTIME_REACHED: "MaxAutoMLJobRuntimeReached",
    MAX_CANDIDATES_REACHED: "MaxCandidatesReached",
    MODEL_DEPLOYMENT_ERROR: "ModelDeploymentError",
    MODEL_INSIGHTS_ERROR: "ModelInsightsError",
    MODEL_TUNING: "ModelTuning",
    PRE_TRAINING: "PreTraining",
    STARTING: "Starting",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
    TRAINING_MODELS: "TrainingModels",
};
export const AutoMLJobStatus = {
    COMPLETED: "Completed",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
};
export const ProblemType = {
    BINARY_CLASSIFICATION: "BinaryClassification",
    MULTICLASS_CLASSIFICATION: "MulticlassClassification",
    REGRESSION: "Regression",
};
export const FillingType = {
    Backfill: "backfill",
    BackfillValue: "backfill_value",
    Frontfill: "frontfill",
    FrontfillValue: "frontfill_value",
    Futurefill: "futurefill",
    FuturefillValue: "futurefill_value",
    Middlefill: "middlefill",
    MiddlefillValue: "middlefill_value",
};
export var AutoMLProblemTypeConfig;
(function (AutoMLProblemTypeConfig) {
    AutoMLProblemTypeConfig.visit = (value, visitor) => {
        if (value.ImageClassificationJobConfig !== undefined)
            return visitor.ImageClassificationJobConfig(value.ImageClassificationJobConfig);
        if (value.TextClassificationJobConfig !== undefined)
            return visitor.TextClassificationJobConfig(value.TextClassificationJobConfig);
        if (value.TimeSeriesForecastingJobConfig !== undefined)
            return visitor.TimeSeriesForecastingJobConfig(value.TimeSeriesForecastingJobConfig);
        if (value.TabularJobConfig !== undefined)
            return visitor.TabularJobConfig(value.TabularJobConfig);
        if (value.TextGenerationJobConfig !== undefined)
            return visitor.TextGenerationJobConfig(value.TextGenerationJobConfig);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(AutoMLProblemTypeConfig || (AutoMLProblemTypeConfig = {}));
export const AutoMLProblemTypeConfigName = {
    IMAGE_CLASSIFICATION: "ImageClassification",
    TABULAR: "Tabular",
    TEXT_CLASSIFICATION: "TextClassification",
    TEXT_GENERATION: "TextGeneration",
    TIMESERIES_FORECASTING: "TimeSeriesForecasting",
};
export var AutoMLProblemTypeResolvedAttributes;
(function (AutoMLProblemTypeResolvedAttributes) {
    AutoMLProblemTypeResolvedAttributes.visit = (value, visitor) => {
        if (value.TabularResolvedAttributes !== undefined)
            return visitor.TabularResolvedAttributes(value.TabularResolvedAttributes);
        if (value.TextGenerationResolvedAttributes !== undefined)
            return visitor.TextGenerationResolvedAttributes(value.TextGenerationResolvedAttributes);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(AutoMLProblemTypeResolvedAttributes || (AutoMLProblemTypeResolvedAttributes = {}));
export const AutoMLSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const AutoMLSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const AutoMountHomeEFS = {
    DEFAULT_AS_DOMAIN: "DefaultAsDomain",
    DISABLED: "Disabled",
    ENABLED: "Enabled",
};
export const AutotuneMode = {
    ENABLED: "Enabled",
};
export const AwsManagedHumanLoopRequestSource = {
    REKOGNITION_DETECT_MODERATION_LABELS_IMAGE_V3: "AWS/Rekognition/DetectModerationLabels/Image/V3",
    TEXTRACT_ANALYZE_DOCUMENT_FORMS_V1: "AWS/Textract/AnalyzeDocument/Forms/V1",
};
export const BatchDeleteClusterNodesErrorCode = {
    INVALID_NODE_STATUS: "InvalidNodeStatus",
    NODE_ID_IN_USE: "NodeIdInUse",
    NODE_ID_NOT_FOUND: "NodeIdNotFound",
};
export const ModelApprovalStatus = {
    APPROVED: "Approved",
    PENDING_MANUAL_APPROVAL: "PendingManualApproval",
    REJECTED: "Rejected",
};
export const ModelPackageStatus = {
    COMPLETED: "Completed",
    DELETING: "Deleting",
    FAILED: "Failed",
    IN_PROGRESS: "InProgress",
    PENDING: "Pending",
};
export const ProcessingS3DataDistributionType = {
    FULLYREPLICATED: "FullyReplicated",
    SHARDEDBYS3KEY: "ShardedByS3Key",
};
export const ProcessingS3InputMode = {
    FILE: "File",
    PIPE: "Pipe",
};
export const CapacitySizeType = {
    CAPACITY_PERCENT: "CAPACITY_PERCENT",
    INSTANCE_COUNT: "INSTANCE_COUNT",
};
export const TrafficRoutingConfigType = {
    ALL_AT_ONCE: "ALL_AT_ONCE",
    CANARY: "CANARY",
    LINEAR: "LINEAR",
};
export const BooleanOperator = {
    AND: "And",
    OR: "Or",
};
export const CandidateSortBy = {
    CreationTime: "CreationTime",
    FinalObjectiveMetricValue: "FinalObjectiveMetricValue",
    Status: "Status",
};
export const DataSourceName = {
    SalesforceGenie: "SalesforceGenie",
    Snowflake: "Snowflake",
};
export const NodeUnavailabilityType = {
    CAPACITY_PERCENTAGE: "CAPACITY_PERCENTAGE",
    INSTANCE_COUNT: "INSTANCE_COUNT",
};
export const CaptureMode = {
    INPUT: "Input",
    INPUT_AND_OUTPUT: "InputAndOutput",
    OUTPUT: "Output",
};
export const CaptureStatus = {
    STARTED: "Started",
    STOPPED: "Stopped",
};
export const ClarifyFeatureType = {
    CATEGORICAL: "categorical",
    NUMERICAL: "numerical",
    TEXT: "text",
};
export const ClarifyTextGranularity = {
    PARAGRAPH: "paragraph",
    SENTENCE: "sentence",
    TOKEN: "token",
};
export const ClarifyTextLanguage = {
    AFRIKAANS: "af",
    ALBANIAN: "sq",
    ARABIC: "ar",
    ARMENIAN: "hy",
    BASQUE: "eu",
    BENGALI: "bn",
    BULGARIAN: "bg",
    CATALAN: "ca",
    CHINESE: "zh",
    CROATIAN: "hr",
    CZECH: "cs",
    DANISH: "da",
    DUTCH: "nl",
    ENGLISH: "en",
    ESTONIAN: "et",
    FINNISH: "fi",
    FRENCH: "fr",
    GERMAN: "de",
    GREEK: "el",
    GUJARATI: "gu",
    HEBREW: "he",
    HINDI: "hi",
    HUNGARIAN: "hu",
    ICELANDIC: "is",
    INDONESIAN: "id",
    IRISH: "ga",
    ITALIAN: "it",
    KANNADA: "kn",
    KYRGYZ: "ky",
    LATVIAN: "lv",
    LIGURIAN: "lij",
    LITHUANIAN: "lt",
    LUXEMBOURGISH: "lb",
    MACEDONIAN: "mk",
    MALAYALAM: "ml",
    MARATHI: "mr",
    MULTI_LANGUAGE: "xx",
    NEPALI: "ne",
    NORWEGIAN_BOKMAL: "nb",
    PERSIAN: "fa",
    POLISH: "pl",
    PORTUGUESE: "pt",
    ROMANIAN: "ro",
    RUSSIAN: "ru",
    SANSKRIT: "sa",
    SERBIAN: "sr",
    SETSWANA: "tn",
    SINHALA: "si",
    SLOVAK: "sk",
    SLOVENIAN: "sl",
    SPANISH: "es",
    SWEDISH: "sv",
    TAGALOG: "tl",
    TAMIL: "ta",
    TATAR: "tt",
    TELUGU: "te",
    TURKISH: "tr",
    UKRAINIAN: "uk",
    URDU: "ur",
    YORUBA: "yo",
};
export var ClusterInstanceStorageConfig;
(function (ClusterInstanceStorageConfig) {
    ClusterInstanceStorageConfig.visit = (value, visitor) => {
        if (value.EbsVolumeConfig !== undefined)
            return visitor.EbsVolumeConfig(value.EbsVolumeConfig);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(ClusterInstanceStorageConfig || (ClusterInstanceStorageConfig = {}));
export const ClusterInstanceType = {
    ML_C5N_18XLARGE: "ml.c5n.18xlarge",
    ML_C5N_2XLARGE: "ml.c5n.2xlarge",
    ML_C5N_4XLARGE: "ml.c5n.4xlarge",
    ML_C5N_9XLARGE: "ml.c5n.9xlarge",
    ML_C5N_LARGE: "ml.c5n.large",
    ML_C5_12XLARGE: "ml.c5.12xlarge",
    ML_C5_18XLARGE: "ml.c5.18xlarge",
    ML_C5_24XLARGE: "ml.c5.24xlarge",
    ML_C5_2XLARGE: "ml.c5.2xlarge",
    ML_C5_4XLARGE: "ml.c5.4xlarge",
    ML_C5_9XLARGE: "ml.c5.9xlarge",
    ML_C5_LARGE: "ml.c5.large",
    ML_C5_XLARGE: "ml.c5.xlarge",
    ML_C6I_12XLARGE: "ml.c6i.12xlarge",
    ML_C6I_16XLARGE: "ml.c6i.16xlarge",
    ML_C6I_24XLARGE: "ml.c6i.24xlarge",
    ML_C6I_2XLARGE: "ml.c6i.2xlarge",
    ML_C6I_32XLARGE: "ml.c6i.32xlarge",
    ML_C6I_4XLARGE: "ml.c6i.4xlarge",
    ML_C6I_8XLARGE: "ml.c6i.8xlarge",
    ML_C6I_LARGE: "ml.c6i.large",
    ML_C6I_XLARGE: "ml.c6i.xlarge",
    ML_G5_12XLARGE: "ml.g5.12xlarge",
    ML_G5_16XLARGE: "ml.g5.16xlarge",
    ML_G5_24XLARGE: "ml.g5.24xlarge",
    ML_G5_2XLARGE: "ml.g5.2xlarge",
    ML_G5_48XLARGE: "ml.g5.48xlarge",
    ML_G5_4XLARGE: "ml.g5.4xlarge",
    ML_G5_8XLARGE: "ml.g5.8xlarge",
    ML_G5_XLARGE: "ml.g5.xlarge",
    ML_G6E_12XLARGE: "ml.g6e.12xlarge",
    ML_G6E_16XLARGE: "ml.g6e.16xlarge",
    ML_G6E_24XLARGE: "ml.g6e.24xlarge",
    ML_G6E_2XLARGE: "ml.g6e.2xlarge",
    ML_G6E_48XLARGE: "ml.g6e.48xlarge",
    ML_G6E_4XLARGE: "ml.g6e.4xlarge",
    ML_G6E_8XLARGE: "ml.g6e.8xlarge",
    ML_G6E_XLARGE: "ml.g6e.xlarge",
    ML_G6_12XLARGE: "ml.g6.12xlarge",
    ML_G6_16XLARGE: "ml.g6.16xlarge",
    ML_G6_24XLARGE: "ml.g6.24xlarge",
    ML_G6_2XLARGE: "ml.g6.2xlarge",
    ML_G6_48XLARGE: "ml.g6.48xlarge",
    ML_G6_4XLARGE: "ml.g6.4xlarge",
    ML_G6_8XLARGE: "ml.g6.8xlarge",
    ML_G6_XLARGE: "ml.g6.xlarge",
    ML_GR6_4XLARGE: "ml.gr6.4xlarge",
    ML_GR6_8XLARGE: "ml.gr6.8xlarge",
    ML_I3EN_12XLARGE: "ml.i3en.12xlarge",
    ML_I3EN_24XLARGE: "ml.i3en.24xlarge",
    ML_I3EN_2XLARGE: "ml.i3en.2xlarge",
    ML_I3EN_3XLARGE: "ml.i3en.3xlarge",
    ML_I3EN_6XLARGE: "ml.i3en.6xlarge",
    ML_I3EN_LARGE: "ml.i3en.large",
    ML_I3EN_XLARGE: "ml.i3en.xlarge",
    ML_M5_12XLARGE: "ml.m5.12xlarge",
    ML_M5_16XLARGE: "ml.m5.16xlarge",
    ML_M5_24XLARGE: "ml.m5.24xlarge",
    ML_M5_2XLARGE: "ml.m5.2xlarge",
    ML_M5_4XLARGE: "ml.m5.4xlarge",
    ML_M5_8XLARGE: "ml.m5.8xlarge",
    ML_M5_LARGE: "ml.m5.large",
    ML_M5_XLARGE: "ml.m5.xlarge",
    ML_M6I_12XLARGE: "ml.m6i.12xlarge",
    ML_M6I_16XLARGE: "ml.m6i.16xlarge",
    ML_M6I_24XLARGE: "ml.m6i.24xlarge",
    ML_M6I_2XLARGE: "ml.m6i.2xlarge",
    ML_M6I_32XLARGE: "ml.m6i.32xlarge",
    ML_M6I_4XLARGE: "ml.m6i.4xlarge",
    ML_M6I_8XLARGE: "ml.m6i.8xlarge",
    ML_M6I_LARGE: "ml.m6i.large",
    ML_M6I_XLARGE: "ml.m6i.xlarge",
    ML_M7I_12XLARGE: "ml.m7i.12xlarge",
    ML_M7I_16XLARGE: "ml.m7i.16xlarge",
    ML_M7I_24XLARGE: "ml.m7i.24xlarge",
    ML_M7I_2XLARGE: "ml.m7i.2xlarge",
    ML_M7I_48XLARGE: "ml.m7i.48xlarge",
    ML_M7I_4XLARGE: "ml.m7i.4xlarge",
    ML_M7I_8XLARGE: "ml.m7i.8xlarge",
    ML_M7I_LARGE: "ml.m7i.large",
    ML_M7I_XLARGE: "ml.m7i.xlarge",
    ML_P4DE_24XLARGE: "ml.p4de.24xlarge",
    ML_P4D_24XLARGE: "ml.p4d.24xlarge",
    ML_P5EN_48XLARGE: "ml.p5en.48xlarge",
    ML_P5E_48XLARGE: "ml.p5e.48xlarge",
    ML_P5_48XLARGE: "ml.p5.48xlarge",
    ML_R6I_12XLARGE: "ml.r6i.12xlarge",
    ML_R6I_16XLARGE: "ml.r6i.16xlarge",
    ML_R6I_24XLARGE: "ml.r6i.24xlarge",
    ML_R6I_2XLARGE: "ml.r6i.2xlarge",
    ML_R6I_32XLARGE: "ml.r6i.32xlarge",
    ML_R6I_4XLARGE: "ml.r6i.4xlarge",
    ML_R6I_8XLARGE: "ml.r6i.8xlarge",
    ML_R6I_LARGE: "ml.r6i.large",
    ML_R6I_XLARGE: "ml.r6i.xlarge",
    ML_R7I_12XLARGE: "ml.r7i.12xlarge",
    ML_R7I_16XLARGE: "ml.r7i.16xlarge",
    ML_R7I_24XLARGE: "ml.r7i.24xlarge",
    ML_R7I_2XLARGE: "ml.r7i.2xlarge",
    ML_R7I_48XLARGE: "ml.r7i.48xlarge",
    ML_R7I_4XLARGE: "ml.r7i.4xlarge",
    ML_R7I_8XLARGE: "ml.r7i.8xlarge",
    ML_R7I_LARGE: "ml.r7i.large",
    ML_R7I_XLARGE: "ml.r7i.xlarge",
    ML_T3_2XLARGE: "ml.t3.2xlarge",
    ML_T3_LARGE: "ml.t3.large",
    ML_T3_MEDIUM: "ml.t3.medium",
    ML_T3_XLARGE: "ml.t3.xlarge",
    ML_TRN1N_32XLARGE: "ml.trn1n.32xlarge",
    ML_TRN1_32XLARGE: "ml.trn1.32xlarge",
    ML_TRN2_48XLARGE: "ml.trn2.48xlarge",
};
export const DeepHealthCheckType = {
    INSTANCE_CONNECTIVITY: "InstanceConnectivity",
    INSTANCE_STRESS: "InstanceStress",
};
export const InstanceGroupStatus = {
    CREATING: "Creating",
    DEGRADED: "Degraded",
    DELETING: "Deleting",
    FAILED: "Failed",
    INSERVICE: "InService",
    SYSTEMUPDATING: "SystemUpdating",
    UPDATING: "Updating",
};
export const ClusterInstanceStatus = {
    DEEP_HEALTH_CHECK_IN_PROGRESS: "DeepHealthCheckInProgress",
    FAILURE: "Failure",
    PENDING: "Pending",
    RUNNING: "Running",
    SHUTTING_DOWN: "ShuttingDown",
    SYSTEM_UPDATING: "SystemUpdating",
};
export const ClusterNodeRecovery = {
    AUTOMATIC: "Automatic",
    NONE: "None",
};
export const SchedulerResourceStatus = {
    CREATED: "Created",
    CREATE_FAILED: "CreateFailed",
    CREATE_ROLLBACK_FAILED: "CreateRollbackFailed",
    CREATING: "Creating",
    DELETED: "Deleted",
    DELETE_FAILED: "DeleteFailed",
    DELETE_ROLLBACK_FAILED: "DeleteRollbackFailed",
    DELETING: "Deleting",
    UPDATED: "Updated",
    UPDATE_FAILED: "UpdateFailed",
    UPDATE_ROLLBACK_FAILED: "UpdateRollbackFailed",
    UPDATING: "Updating",
};
export const ClusterSortBy = {
    CREATION_TIME: "CREATION_TIME",
    NAME: "NAME",
};
export const ClusterStatus = {
    CREATING: "Creating",
    DELETING: "Deleting",
    FAILED: "Failed",
    INSERVICE: "InService",
    ROLLINGBACK: "RollingBack",
    SYSTEMUPDATING: "SystemUpdating",
    UPDATING: "Updating",
};
export const CodeRepositorySortBy = {
    CREATION_TIME: "CreationTime",
    LAST_MODIFIED_TIME: "LastModifiedTime",
    NAME: "Name",
};
export const CodeRepositorySortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export var CollectionConfig;
(function (CollectionConfig) {
    CollectionConfig.visit = (value, visitor) => {
        if (value.VectorConfig !== undefined)
            return visitor.VectorConfig(value.VectorConfig);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(CollectionConfig || (CollectionConfig = {}));
export const CollectionType = {
    LIST: "List",
    SET: "Set",
    VECTOR: "Vector",
};
export const CompilationJobStatus = {
    COMPLETED: "COMPLETED",
    FAILED: "FAILED",
    INPROGRESS: "INPROGRESS",
    STARTING: "STARTING",
    STOPPED: "STOPPED",
    STOPPING: "STOPPING",
};
export const TargetDevice = {
    AISAGE: "aisage",
    AMBA_CV2: "amba_cv2",
    AMBA_CV22: "amba_cv22",
    AMBA_CV25: "amba_cv25",
    COREML: "coreml",
    DEEPLENS: "deeplens",
    IMX8MPLUS: "imx8mplus",
    IMX8QM: "imx8qm",
    JACINTO_TDA4VM: "jacinto_tda4vm",
    JETSON_NANO: "jetson_nano",
    JETSON_TX1: "jetson_tx1",
    JETSON_TX2: "jetson_tx2",
    JETSON_XAVIER: "jetson_xavier",
    LAMBDA: "lambda",
    ML_C4: "ml_c4",
    ML_C5: "ml_c5",
    ML_C6G: "ml_c6g",
    ML_EIA2: "ml_eia2",
    ML_G4DN: "ml_g4dn",
    ML_INF1: "ml_inf1",
    ML_INF2: "ml_inf2",
    ML_M4: "ml_m4",
    ML_M5: "ml_m5",
    ML_M6G: "ml_m6g",
    ML_P2: "ml_p2",
    ML_P3: "ml_p3",
    ML_TRN1: "ml_trn1",
    QCS603: "qcs603",
    QCS605: "qcs605",
    RASP3B: "rasp3b",
    RASP4B: "rasp4b",
    RK3288: "rk3288",
    RK3399: "rk3399",
    SBE_C: "sbe_c",
    SITARA_AM57X: "sitara_am57x",
    X86_WIN32: "x86_win32",
    X86_WIN64: "x86_win64",
};
export const TargetPlatformAccelerator = {
    INTEL_GRAPHICS: "INTEL_GRAPHICS",
    MALI: "MALI",
    NNA: "NNA",
    NVIDIA: "NVIDIA",
};
export const TargetPlatformArch = {
    ARM64: "ARM64",
    ARM_EABI: "ARM_EABI",
    ARM_EABIHF: "ARM_EABIHF",
    X86: "X86",
    X86_64: "X86_64",
};
export const TargetPlatformOs = {
    ANDROID: "ANDROID",
    LINUX: "LINUX",
};
export const CompleteOnConvergence = {
    DISABLED: "Disabled",
    ENABLED: "Enabled",
};
export const PreemptTeamTasks = {
    LOWERPRIORITY: "LowerPriority",
    NEVER: "Never",
};
export const ResourceSharingStrategy = {
    DONTLEND: "DontLend",
    LEND: "Lend",
    LENDANDBORROW: "LendAndBorrow",
};
export const ConditionOutcome = {
    FALSE: "False",
    TRUE: "True",
};
export class ConflictException extends __BaseException {
    name = "ConflictException";
    $fault = "client";
    Message;
    constructor(opts) {
        super({
            name: "ConflictException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ConflictException.prototype);
        this.Message = opts.Message;
    }
}
export const RepositoryAccessMode = {
    PLATFORM: "Platform",
    VPC: "Vpc",
};
export const ContainerMode = {
    MULTI_MODEL: "MultiModel",
    SINGLE_MODEL: "SingleModel",
};
export const ModelCacheSetting = {
    DISABLED: "Disabled",
    ENABLED: "Enabled",
};
