import { SENSITIVE_STRING } from "@smithy/smithy-client";
import { ModelPackageModelCardFilterSensitiveLog, } from "./models_1";
export const SortQuotaBy = {
    CLUSTER_ARN: "ClusterArn",
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const SortContextsBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const MonitoringJobDefinitionSortKey = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const ListDeviceFleetsSortBy = {
    CreationTime: "CREATION_TIME",
    LastModifiedTime: "LAST_MODIFIED_TIME",
    Name: "NAME",
};
export const ListEdgeDeploymentPlansSortBy = {
    CreationTime: "CREATION_TIME",
    DeviceFleetName: "DEVICE_FLEET_NAME",
    LastModifiedTime: "LAST_MODIFIED_TIME",
    Name: "NAME",
};
export const ListEdgePackagingJobsSortBy = {
    CreationTime: "CREATION_TIME",
    EdgePackagingJobStatus: "STATUS",
    LastModifiedTime: "LAST_MODIFIED_TIME",
    ModelName: "MODEL_NAME",
    Name: "NAME",
};
export const OrderKey = {
    Ascending: "Ascending",
    Descending: "Descending",
};
export const SortExperimentsBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const SortInferenceExperimentsBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const ListInferenceRecommendationsJobsSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const SortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const ListLabelingJobsForWorkteamSortByOptions = {
    CREATION_TIME: "CreationTime",
};
export const SortLineageGroupsBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const SortTrackingServerBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const ModelCardExportJobSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const ModelCardExportJobSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const ModelCardSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const ModelCardSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const ModelCardVersionSortBy = {
    VERSION: "Version",
};
export const ModelMetadataFilterType = {
    DOMAIN: "Domain",
    FRAMEWORK: "Framework",
    FRAMEWORKVERSION: "FrameworkVersion",
    TASK: "Task",
};
export const ModelPackageGroupSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const ModelPackageType = {
    BOTH: "Both",
    UNVERSIONED: "Unversioned",
    VERSIONED: "Versioned",
};
export const ModelPackageSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const ModelSortKey = {
    CreationTime: "CreationTime",
    Name: "Name",
};
export const MonitoringAlertHistorySortKey = {
    CreationTime: "CreationTime",
    Status: "Status",
};
export const MonitoringAlertStatus = {
    IN_ALERT: "InAlert",
    OK: "OK",
};
export const MonitoringExecutionSortKey = {
    CREATION_TIME: "CreationTime",
    SCHEDULED_TIME: "ScheduledTime",
    STATUS: "Status",
};
export const MonitoringScheduleSortKey = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const NotebookInstanceLifecycleConfigSortKey = {
    CREATION_TIME: "CreationTime",
    LAST_MODIFIED_TIME: "LastModifiedTime",
    NAME: "Name",
};
export const NotebookInstanceLifecycleConfigSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const NotebookInstanceSortKey = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const NotebookInstanceSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const ListOptimizationJobsSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
    STATUS: "Status",
};
export const SortPipelineExecutionsBy = {
    CREATION_TIME: "CreationTime",
    PIPELINE_EXECUTION_ARN: "PipelineExecutionArn",
};
export const StepStatus = {
    EXECUTING: "Executing",
    FAILED: "Failed",
    STARTING: "Starting",
    STOPPED: "Stopped",
    STOPPING: "Stopping",
    SUCCEEDED: "Succeeded",
};
export const SortPipelinesBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const ProjectSortBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const ProjectSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const ResourceCatalogSortBy = {
    CREATION_TIME: "CreationTime",
};
export const ResourceCatalogSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const SpaceSortKey = {
    CreationTime: "CreationTime",
    LastModifiedTime: "LastModifiedTime",
};
export const StudioLifecycleConfigSortKey = {
    CreationTime: "CreationTime",
    LastModifiedTime: "LastModifiedTime",
    Name: "Name",
};
export const TrainingJobSortByOptions = {
    CreationTime: "CreationTime",
    FinalObjectiveMetricValue: "FinalObjectiveMetricValue",
    Name: "Name",
    Status: "Status",
};
export const TrainingPlanFilterName = {
    STATUS: "Status",
};
export const TrainingPlanSortBy = {
    NAME: "TrainingPlanName",
    START_TIME: "StartTime",
    STATUS: "Status",
};
export const TrainingPlanSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const SortTrialComponentsBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const SortTrialsBy = {
    CREATION_TIME: "CreationTime",
    NAME: "Name",
};
export const UserProfileSortKey = {
    CreationTime: "CreationTime",
    LastModifiedTime: "LastModifiedTime",
};
export const ListWorkforcesSortByOptions = {
    CreateDate: "CreateDate",
    Name: "Name",
};
export const ListWorkteamsSortByOptions = {
    CreateDate: "CreateDate",
    Name: "Name",
};
export const ModelVariantAction = {
    PROMOTE: "Promote",
    REMOVE: "Remove",
    RETAIN: "Retain",
};
export const Relation = {
    EQUAL_TO: "EqualTo",
    GREATER_THAN_OR_EQUAL_TO: "GreaterThanOrEqualTo",
};
export const SearchSortOrder = {
    ASCENDING: "Ascending",
    DESCENDING: "Descending",
};
export const ModelCardFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Content && { Content: SENSITIVE_STRING }),
});
export const ModelPackageFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.ModelCard && { ModelCard: ModelPackageModelCardFilterSensitiveLog(obj.ModelCard) }),
});
export const SearchRecordFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.TrialComponent && { TrialComponent: obj.TrialComponent }),
    ...(obj.ModelPackage && { ModelPackage: ModelPackageFilterSensitiveLog(obj.ModelPackage) }),
    ...(obj.FeatureGroup && { FeatureGroup: obj.FeatureGroup }),
    ...(obj.ModelCard && { ModelCard: ModelCardFilterSensitiveLog(obj.ModelCard) }),
});
export const SearchResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.Results && { Results: obj.Results.map((item) => SearchRecordFilterSensitiveLog(item)) }),
});
