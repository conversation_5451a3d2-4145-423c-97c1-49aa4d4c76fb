{"version": 3, "file": "FontLoader.web.js", "sourceRoot": "", "sources": ["../src/FontLoader.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,OAAO,cAAc,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAA4B,WAAW,EAAE,MAAM,cAAc,CAAC;AAErE,SAAS,iBAAiB,CAAC,KAAU;IACnC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,KAAK,IAAI,IAAI,CAAC;KACtB;SAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACpC,OAAO,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC;KAC7D;SAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACpC,OAAO,iBAAiB,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;KACnD;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,qBAAqB,CAAC,KAAU;IACvC,OAAO,KAAK,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC;AAC3C,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAC,IAAY;IACjD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,MAAkB;IAClD,MAAM,GAAG,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;IACtC,MAAM,OAAO,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAE9C,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QACnC,uBAAuB,CAAC,GAAG,CAAC,CAAC;KAC9B;IAED,OAAO;QACL,GAAG,EAAE,GAAI;QACT,OAAO;KACR,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAAC,MAAW;IAC1C,IAAI,IAAI,GAAW,OAAO,MAAM,CAAC;IACjC,IAAI,IAAI,KAAK,QAAQ;QAAE,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9D,MAAM,IAAI,UAAU,CAClB,iBAAiB,EACjB,8EAA8E,IAAI,EAAE,CACrF,CAAC;AACJ,CAAC;AAED,qCAAqC;AACrC,MAAM,UAAU,mBAAmB,CAAC,IAAY,EAAE,KAA2B;IAC3E,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ,IAAK,KAAa,CAAC,aAAa,EAAE;QAC9F,uBAAuB,CAAC,KAAK,CAAC,CAAC;KAChC;IAED,IAAI;QACF,OAAO,cAAc,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAC9C;IAAC,MAAM;QACN,SAAS;KACV;IAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;AAC3B,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,IAAY;IAC5C,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import { Asset } from 'expo-asset';\nimport { CodedError } from 'expo-modules-core';\n\nimport ExpoFontLoader from './ExpoFontLoader';\nimport { FontResource, FontSource, FontDisplay } from './Font.types';\n\nfunction uriFromFontSource(asset: any): string | null {\n  if (typeof asset === 'string') {\n    return asset || null;\n  } else if (typeof asset === 'object') {\n    return asset.uri || asset.localUri || asset.default || null;\n  } else if (typeof asset === 'number') {\n    return uriFromFontSource(Asset.fromModule(asset));\n  }\n  return null;\n}\n\nfunction displayFromFontSource(asset: any): FontDisplay | undefined {\n  return asset.display || FontDisplay.AUTO;\n}\n\nexport function fontFamilyNeedsScoping(name: string): boolean {\n  return false;\n}\n\nexport function getAssetForSource(source: FontSource): Asset | FontResource {\n  const uri = uriFromFontSource(source);\n  const display = displayFromFontSource(source);\n\n  if (!uri || typeof uri !== 'string') {\n    throwInvalidSourceError(uri);\n  }\n\n  return {\n    uri: uri!,\n    display,\n  };\n}\n\nfunction throwInvalidSourceError(source: any): never {\n  let type: string = typeof source;\n  if (type === 'object') type = JSON.stringify(source, null, 2);\n  throw new CodedError(\n    `ERR_FONT_SOURCE`,\n    `Expected font asset of type \\`string | FontResource | Asset\\` instead got: ${type}`\n  );\n}\n\n// NOTE(EvanBacon): No async keyword!\nexport function loadSingleFontAsync(name: string, input: Asset | FontResource): Promise<void> {\n  if (typeof input !== 'object' || typeof input.uri !== 'string' || (input as any).downloadAsync) {\n    throwInvalidSourceError(input);\n  }\n\n  try {\n    return ExpoFontLoader.loadAsync(name, input);\n  } catch {\n    // No-op.\n  }\n\n  return Promise.resolve();\n}\n\nexport function getNativeFontName(name: string): string {\n  return name;\n}\n"]}