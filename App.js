import React, { useState, useEffect } from 'react';
import { useColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { NavigationContainer, DefaultTheme, DarkTheme } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { AuthProvider, useAuth } from './src/contexts/AuthContext';
import { ToastProvider } from './src/contexts/ToastContext';
import { AuthScreen } from './src/components/AuthScreen';
import { CameraScreen } from './src/components/CameraScreen';
import { FormHistoryScreen } from './src/components/FormHistoryScreen';
import { Toast } from './src/components/Toast';
import { LoadingScreen } from './src/components/LoadingScreen';
import Onboarding from './src/components/Onboarding';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

const lightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: '#2196F3',
    background: '#f9f9f9',
    card: '#ffffff',
    text: '#222222',
    border: '#e0e0e0',
    notification: '#2196F3',
  },
  fonts: {
    regular: 'System',
    medium: 'System',
    light: 'System',
    thin: 'System',
  },
};

const darkTheme = {
  ...DarkTheme,
  colors: {
    ...DarkTheme.colors,
    primary: '#2196F3',
    background: '#121212',
    card: '#1e1e1e',
    text: '#ffffff',
    border: '#272727',
    notification: '#2196F3',
  },
  fonts: {
    regular: 'System',
    medium: 'System',
    light: 'System',
    thin: 'System',
  },
};

function MainTabs() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Camera') {
            iconName = focused ? 'camera' : 'camera-outline';
          } else if (route.name === 'History') {
            iconName = focused ? 'list' : 'list-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: lightTheme.colors.primary,
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          paddingVertical: 12,
          paddingHorizontal: 20,
          elevation: 8,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          backgroundColor: lightTheme.colors.card,
          position: 'absolute',
          height: 70,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: -3 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        headerStyle: {
          backgroundColor: lightTheme.colors.primary,
        },
        headerTintColor: '#fff',
        headerTitleStyle: {
          fontWeight: '600',
          fontSize: 20,
        },
      })}
    >
      <Tab.Screen
        name="Camera"
        component={CameraScreen}
        options={{
          title: 'Scan Form',
        }}
      />
      <Tab.Screen
        name="History"
        component={FormHistoryScreen}
        options={{
          title: 'Form History',
        }}
      />
    </Tab.Navigator>
  );
}

function Navigation({ isOnboardingComplete, setIsOnboardingComplete }) {
  const { user } = useAuth();

  return (
    <>
      <Stack.Navigator>
        {!isOnboardingComplete ? (
          <Stack.Screen
            name="Onboarding"
            options={{ headerShown: false }}
          >
            {(props) => <Onboarding {...props} onDone={() => setIsOnboardingComplete(true)} />}
          </Stack.Screen>
        ) : !user ? (
          <Stack.Screen
            name="Auth"
            component={AuthScreen}
            options={{ headerShown: false }}
          />
        ) : (
          <Stack.Screen
            name="Main"
            component={MainTabs}
            options={{ headerShown: false }}
          />
        )}
      </Stack.Navigator>
      <Toast />
    </>
  );
}

export default function App() {
  const scheme = useColorScheme();
  const [isOnboardingComplete, setIsOnboardingComplete] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function checkOnboardingStatus() {
      try {
        const value = await AsyncStorage.getItem('@onboardingComplete');
        if (value !== null) {
          setIsOnboardingComplete(true);
        }
      } catch (e) {
        // error reading value
      } finally {
        setLoading(false);
      }
    }
    checkOnboardingStatus();
  }, []);

  useEffect(() => {
    async function storeOnboardingStatus() {
      if (isOnboardingComplete) {
        try {
          await AsyncStorage.setItem('@onboardingComplete', 'true');
        } catch (e) {
          // saving error
        }
      }
    }
    storeOnboardingStatus();
  }, [isOnboardingComplete]);

  if (loading) {
    return <LoadingScreen message="Initializing app..." />;
  }

  const theme = scheme === 'dark' ? darkTheme : lightTheme;

  return (
    <SafeAreaProvider>
      <AuthProvider>
        <ToastProvider>
          <NavigationContainer theme={theme}>
            <Navigation isOnboardingComplete={isOnboardingComplete} setIsOnboardingComplete={setIsOnboardingComplete} />
            <StatusBar style={scheme === 'dark' ? 'light' : 'auto'} />
          </NavigationContainer>
        </ToastProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}