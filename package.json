{"name": "formwise", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~3.1.3", "@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "1.21.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@supabase/supabase-js": "^2.39.3", "cohere-ai": "^7.0.0", "expo": "~50.0.0", "expo-camera": "~14.1.3", "expo-file-system": "~16.0.5", "expo-font": "~11.10.3", "expo-image-picker": "~14.7.1", "expo-speech": "~11.7.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-dom": "^18.2.0", "react-native": "0.73.6", "react-native-fs": "^2.20.0", "react-native-onboarding-swiper": "^1.3.0", "react-native-paper": "^5.12.1", "react-native-pdf": "^6.7.4", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-web": "^0.19.13", "tesseract.js": "^5.0.3", "expo-print": "~12.8.1", "expo-sharing": "~11.10.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}