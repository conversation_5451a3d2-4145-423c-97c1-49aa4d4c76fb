import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Text, ActivityIndicator, Alert, Modal, Pressable, Animated, Easing } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useToast } from '../contexts/ToastContext';
import { getFormData } from '../services/supabase';
import { explainForm, summarizeForm } from '../services/cohere';
import { generatePDF } from '../services/pdfGenerator';
import { voiceChat } from '../services/voiceChat';

export const FormHistoryScreen = () => {
  const [forms, setForms] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedForm, setSelectedForm] = useState(null);
  const [explanation, setExplanation] = useState('');
  const [summary, setSummary] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));
  const { user } = useAuth();
  const { showSuccess, showError, showInfo } = useToast();

  useEffect(() => {
    loadForms();
  }, []);

  const loadForms = async () => {
    try {
      const { data, error } = await getFormData(user.id);
      if (error) throw error;
      setForms(data || []);
      if (data && data.length > 0) {
        showInfo(`Loaded ${data.length} form${data.length === 1 ? '' : 's'}`);
      }
    } catch (error) {
      showError('Failed to load forms');
    } finally {
      setLoading(false);
    }
  };

  const handleFormSelect = async (form) => {
    setSelectedForm(form);
    setModalVisible(true);
    try {
      const [explanationResult, summaryResult] = await Promise.all([
        explainForm(form.form_data.text),
        summarizeForm(form.form_data.text)
      ]);
      setExplanation(explanationResult);
      setSummary(summaryResult);
      voiceChat.speak(summaryResult);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }).start();
    } catch (error) {
      showError('Failed to generate explanation');
    }
  };

  const handleGeneratePDF = async (form) => {
    try {
      showInfo('Generating PDF...');
      const { localPath } = await generatePDF(form.form_data, user.id);
      showSuccess('PDF generated and shared successfully!');
    } catch (error) {
      showError('Failed to generate PDF');
    }
  };

  const closeModal = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 200,
      easing: Easing.in(Easing.ease),
      useNativeDriver: true,
    }).start(() => {
      setModalVisible(false);
      setSelectedForm(null);
      setExplanation('');
      setSummary('');
    });
  };

  const renderFormItem = ({ item }) => (
    <TouchableOpacity
      style={styles.formItem}
      onPress={() => handleFormSelect(item)}
      activeOpacity={0.7}
      accessibilityRole="button"
      accessibilityLabel={`Select form from ${new Date(item.created_at).toLocaleDateString()}`}
    >
      <Text style={styles.formDate}>
        {new Date(item.created_at).toLocaleDateString()}
      </Text>
      <Text style={styles.formText} numberOfLines={2}>
        {item.form_data.text}
      </Text>
      <TouchableOpacity
        style={styles.pdfButton}
        onPress={() => handleGeneratePDF(item)}
        activeOpacity={0.7}
        accessibilityRole="button"
        accessibilityLabel="Generate PDF from form"
      >
        <Text style={styles.pdfButtonText}>Generate PDF</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#2196F3" />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={forms}
        renderItem={renderFormItem}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.list}
      />
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={closeModal}
      >
        <Pressable style={styles.modalOverlay} onPress={closeModal}>
          <Animated.View style={[styles.modalContent, { opacity: fadeAnim }]}>
            <Text style={styles.explanationTitle}>Form Summary</Text>
            <Text style={styles.explanationText}>{summary}</Text>
            <Text style={[styles.explanationTitle, styles.marginTop]}>Detailed Explanation</Text>
            <Text style={styles.explanationText}>{explanation}</Text>
            <Pressable
              style={({ pressed }) => [
                styles.closeButton,
                pressed && styles.buttonPressed,
              ]}
              onPress={closeModal}
              accessibilityRole="button"
              accessibilityLabel="Close explanation modal"
            >
              <Text style={styles.closeButtonText}>Close</Text>
            </Pressable>
          </Animated.View>
        </Pressable>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  list: {
    padding: 16,
  },
  formItem: {
    backgroundColor: '#f5f5f5',
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
  },
  formDate: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    fontWeight: '600',
  },
  formText: {
    fontSize: 18,
    color: '#333',
    marginBottom: 12,
  },
  pdfButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignSelf: 'flex-start',
    elevation: 3,
    shadowColor: '#2196F3',
    shadowOpacity: 0.4,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 3 },
  },
  pdfButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    maxHeight: '70%',
    elevation: 10,
    shadowColor: '#000',
    shadowOpacity: 0.25,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 5 },
  },
  explanationTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 12,
    color: '#222',
  },
  explanationText: {
    fontSize: 16,
    lineHeight: 26,
    color: '#444',
  },
  marginTop: {
    marginTop: 24,
  },
  closeButton: {
    marginTop: 30,
    backgroundColor: '#2196F3',
    borderRadius: 10,
    paddingVertical: 14,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#2196F3',
    shadowOpacity: 0.5,
    shadowRadius: 6,
    shadowOffset: { width: 0, height: 3 },
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '700',
  },
  buttonPressed: {
    opacity: 0.75,
  },
});