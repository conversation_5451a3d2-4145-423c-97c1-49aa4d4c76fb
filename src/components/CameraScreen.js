import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ActivityIndicator, Alert, ScrollView, Clipboard, Animated, Easing } from 'react-native';
import { Camera, CameraType } from 'expo-camera';
import { createWorker } from 'tesseract.js';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { saveFormData, uploadFile } from '../services/supabase';
import { useToast } from '../contexts/ToastContext';

export const CameraScreen = () => {
  const [hasPermission, setHasPermission] = useState(null);
  const [camera, setCamera] = useState(null);
  const [loading, setLoading] = useState(false);
  const [ocrResult, setOcrResult] = useState('');
  const { user } = useAuth();
  const { showSuccess, showError, showInfo } = useToast();
  const [copyFeedback, setCopyFeedback] = useState(false);
  const slideAnim = React.useRef(new Animated.Value(300)).current; // for swipeable card

  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  useEffect(() => {
    if (ocrResult) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 300,
        easing: Easing.in(Easing.ease),
        useNativeDriver: false,
      }).start();
    }
  }, [ocrResult]);

  const takePicture = async () => {
    if (camera) {
      setLoading(true);
      setOcrResult('');
      try {
        const photo = await camera.takePictureAsync({
          quality: 1,
          base64: true,
        });

        // Process with Tesseract
        const worker = await createWorker({
          logger: m => {
            // could add progress feedback here if desired
          },
        });
        await worker.loadLanguage('eng');
        await worker.initialize('eng');

        const { data: { text } } = await worker.recognize(photo.uri);
        setOcrResult(text);
        await worker.terminate();

        // Save to Supabase
        if (user) {
          // Upload image
          const { error: uploadError } = await uploadFile({
            uri: photo.uri,
            name: `scan-${Date.now()}.jpg`,
            type: 'image/jpeg',
          });

          if (uploadError) throw uploadError;

          // Save form data
          const { error: saveError } = await saveFormData(user.id, {
            text,
            imageUrl: photo.uri,
            timestamp: new Date().toISOString(),
          });

          if (saveError) throw saveError;

          showSuccess('Document scanned and saved successfully!');
        }
      } catch (error) {
        console.error('Error processing image:', error);
        showError('Failed to process document. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  const copyToClipboard = () => {
    Clipboard.setString(ocrResult);
    showSuccess('Text copied to clipboard!');
    setCopyFeedback(true);
    setTimeout(() => setCopyFeedback(false), 2000);
  };

  if (hasPermission === null) {
    return (
      <View style={styles.containerCentered}>
        <Text style={styles.infoText}>Requesting camera permission...</Text>
      </View>
    );
  }
  if (hasPermission === false) {
    return (
      <View style={styles.containerCentered}>
        <Text style={styles.infoText}>No access to camera</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera
        style={styles.camera}
        type={CameraType.back}
        ref={ref => setCamera(ref)}
      >
        <View style={styles.overlay}>
          <Text style={styles.guideText}>Align document within frame</Text>
          <TouchableOpacity
            style={[styles.captureButton, loading && styles.captureButtonDisabled]}
            onPress={takePicture}
            disabled={loading}
            activeOpacity={0.7}
          >
            {loading ? (
              <ActivityIndicator size="large" color="#fff" />
            ) : (
              <View style={styles.innerCaptureButton} />
            )}
          </TouchableOpacity>
        </View>
      </Camera>
      <Animated.View style={[styles.resultCard, { bottom: slideAnim }]}>
        <View style={styles.resultHeader}>
          <Text style={styles.resultTitle}>OCR Result</Text>
          <TouchableOpacity onPress={copyToClipboard} style={styles.copyButton} activeOpacity={0.7}>
            <Text style={styles.copyButtonText}>{copyFeedback ? 'Copied!' : 'Copy Text'}</Text>
          </TouchableOpacity>
        </View>
        <ScrollView style={styles.resultScroll}>
          <Text selectable style={styles.resultText}>{ocrResult}</Text>
        </ScrollView>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  containerCentered: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  infoText: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingBottom: 40,
  },
  guideText: {
    color: 'rgba(255,255,255,0.85)',
    fontSize: 18,
    marginBottom: 20,
    fontWeight: '600',
    textShadowColor: 'rgba(0,0,0,0.7)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    borderColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(33,150,243,0.3)',
    shadowColor: '#2196F3',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 10,
  },
  captureButtonDisabled: {
    opacity: 0.6,
  },
  innerCaptureButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#2196F3',
  },
  resultCard: {
    position: 'absolute',
    left: 10,
    right: 10,
    height: '40%',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 10,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  resultTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#222',
  },
  copyButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 6,
    paddingHorizontal: 14,
    borderRadius: 20,
    elevation: 3,
  },
  copyButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  resultScroll: {
    flex: 1,
  },
  resultText: {
    fontSize: 16,
    color: '#333',
    lineHeight: 22,
  },
});