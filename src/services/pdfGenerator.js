import * as FileSystem from 'expo-file-system';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { generatePDFContent } from './cohere';
import { uploadFile } from './supabase';

export const generatePDF = async (formData, userId) => {
  try {
    // Get formatted content from Cohere
    const formattedContent = await generatePDFContent(formData);

    // Create PDF content
    const pdfContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Form Document</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              padding: 20px;
              line-height: 1.6;
              color: #333;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #2196F3;
              padding-bottom: 20px;
            }
            .content {
              line-height: 1.6;
              margin: 20px 0;
            }
            .section {
              margin-bottom: 20px;
              padding: 15px;
              background-color: #f5f5f5;
              border-radius: 5px;
            }
            .footer {
              margin-top: 30px;
              text-align: center;
              font-size: 12px;
              color: #666;
              border-top: 1px solid #ddd;
              padding-top: 20px;
            }
            h1 {
              color: #2196F3;
              margin: 0;
            }
            h2 {
              color: #333;
              margin: 15px 0;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Form Document</h1>
            <p>Generated on ${new Date().toLocaleDateString()}</p>
          </div>
          <div class="content">
            ${formattedContent}
          </div>
          <div class="footer">
            <p>Generated by Formwise</p>
            <p>Document ID: ${Date.now()}</p>
          </div>
        </body>
      </html>
    `;

    // Generate actual PDF using expo-print
    const { uri } = await Print.printToFileAsync({
      html: pdfContent,
      base64: false,
    });

    // Move to a more permanent location with better naming
    const fileName = `formwise_scan_${Date.now()}.pdf`;
    const filePath = `${FileSystem.documentDirectory}${fileName}`;

    await FileSystem.moveAsync({
      from: uri,
      to: filePath,
    });

    // Upload to Supabase
    const { error } = await uploadFile({
      uri: filePath,
      name: fileName,
      type: 'application/pdf',
    }, 'pdfs');

    if (error) throw error;

    // Optionally share the PDF
    if (await Sharing.isAvailableAsync()) {
      await Sharing.shareAsync(filePath, {
        mimeType: 'application/pdf',
        dialogTitle: 'Share Scanned Document',
      });
    }

    return {
      localPath: filePath,
      fileName,
    };
  } catch (error) {
    console.error('PDF Generation Error:', error);
    throw error;
  }
};