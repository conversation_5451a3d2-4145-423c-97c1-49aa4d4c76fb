import { CohereClient } from 'cohere-ai';
import { config } from '../config';

const cohere = new CohereClient({
  token: config.COHERE_TRIAL_API_KEY,
});

export const explainForm = async (text) => {
  try {
    const response = await cohere.generate({
      prompt: `You are a helpful assistant that explains government forms in simple terms. Explain the form in Trinidadian English.

Form text: ${text}

Explanation:`,
      max_tokens: 500,
      temperature: 0.7,
      k: 0,
      p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
      stop_sequences: [],
      return_likelihoods: 'NONE',
    });

    return response.generations[0].text.trim();
  } catch (error) {
    console.error('Cohere API Error:', error);
    throw error;
  }
};

export const generatePDFContent = async (formData) => {
  try {
    const response = await cohere.generate({
      prompt: `You are a helpful assistant that formats form data into a clean, professional PDF layout.

Form data: ${JSON.stringify(formData)}

Formatted content:`,
      max_tokens: 1000,
      temperature: 0.5,
      k: 0,
      p: 0.9,
      frequency_penalty: 0,
      presence_penalty: 0,
      stop_sequences: [],
      return_likelihoods: 'NONE',
    });

    return response.generations[0].text.trim();
  } catch (error) {
    console.error('Cohere API Error:', error);
    throw error;
  }
};

export const summarizeForm = async (text) => {
  try {
    const response = await cohere.summarize({
      text: text,
      length: 'medium',
      format: 'paragraph',
      model: 'summarize-xlarge',
      additional_command: 'Focus on the key requirements and important information.',
      temperature: 0.3,
    });

    return response.summary;
  } catch (error) {
    console.error('Cohere API Error:', error);
    throw error;
  }
}; 